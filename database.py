from datetime import datetime
import json
import sqlite3

DB_FILE = "wissen_core.db"


def init_db():
    """Initialize the database with required tables if they don't exist"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()

    cursor.execute('''
     CREATE TABLE IF NOT EXISTS output_persistence (
         id INTEGER PRIMARY KEY AUTOINCREMENT,
         patent_number TEXT NOT NULL,
         novelty_output TEXT,
         infringement_output TEXT,
         claim_chart_output TEXT,
         reverse_engineering_output TEXT,
         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
         updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
         UNIQUE(patent_number)
     )
     ''')

    conn.commit()
    conn.close()


def store_novelty_output(patent_number, output_json):
    """Store novelty agent output"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()

    # Always use json.dumps for proper JSON serialization
    if isinstance(output_json, str):
        # Assume it's already JSON if it's a string
        clean_json_string = output_json
    else:
        # Convert dict, list, or any JSON-serializable object to valid JSON
        clean_json_string = json.dumps(output_json)

    current_time = datetime.now().isoformat()

    cursor.execute('''
        INSERT INTO output_persistence (patent_number, novelty_output, updated_at) 
        VALUES (?, ?, ?)
        ON CONFLICT(patent_number) DO UPDATE SET
        novelty_output = excluded.novelty_output,
        updated_at = excluded.updated_at
    ''', (patent_number, clean_json_string, current_time))

    conn.commit()
    conn.close()


def store_infringement_output(patent_number, output_json):
    """Store infringement agent output"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()

    # Always use json.dumps for proper JSON serialization
    if isinstance(output_json, str):
        # Assume it's already JSON if it's a string
        clean_json_string = output_json
    else:
        # Convert dict, list, or any JSON-serializable object to valid JSON
        clean_json_string = json.dumps(output_json)

    current_time = datetime.now().isoformat()

    cursor.execute('''
        INSERT INTO output_persistence (patent_number, infringement_output, updated_at) 
        VALUES (?, ?, ?)
        ON CONFLICT(patent_number) DO UPDATE SET
        infringement_output = excluded.infringement_output,
        updated_at = excluded.updated_at
    ''', (patent_number, clean_json_string, current_time))

    conn.commit()
    conn.close()


def store_claim_chart_output(patent_number, output_json):
    """Store claim chart output"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()

    # Always use json.dumps for proper JSON serialization
    if isinstance(output_json, str):
        # Assume it's already JSON if it's a string
        clean_json_string = output_json
    else:
        # Convert dict, list, or any JSON-serializable object to valid JSON
        clean_json_string = json.dumps(output_json)

    current_time = datetime.now().isoformat()

    cursor.execute('''
        INSERT INTO output_persistence (patent_number, claim_chart_output, updated_at) 
        VALUES (?, ?, ?)
        ON CONFLICT(patent_number) DO UPDATE SET
        claim_chart_output = excluded.claim_chart_output,
        updated_at = excluded.updated_at
    ''', (patent_number, clean_json_string, current_time))

    conn.commit()
    conn.close()


def store_reverse_engineering_output(patent_number, output_json):
    """Store reverse engineering agent output"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()

    # Always use json.dumps for proper JSON serialization
    if isinstance(output_json, str):
        # Assume it's already JSON if it's a string
        clean_json_string = output_json
    else:
        # Convert dict, list, or any JSON-serializable object to valid JSON
        clean_json_string = json.dumps(output_json)

    current_time = datetime.now().isoformat()

    cursor.execute('''
        INSERT INTO output_persistence (patent_number, reverse_engineering_output, updated_at) 
        VALUES (?, ?, ?)
        ON CONFLICT(patent_number) DO UPDATE SET
        reverse_engineering_output = excluded.reverse_engineering_output,
        updated_at = excluded.updated_at
    ''', (patent_number, clean_json_string, current_time))

    conn.commit()
    conn.close()


def get_stored_outputs(patent_number):
    """Retrieve all stored outputs for a patent number"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()

    cursor.execute('''
        SELECT novelty_output, infringement_output, claim_chart_output, 
               reverse_engineering_output, created_at, updated_at
        FROM output_persistence 
        WHERE patent_number = ?
    ''', (patent_number,))

    result = cursor.fetchone()
    conn.close()

    if result:
        outputs = {}
        field_names = ['novelty_output', 'infringement_output', 'claim_chart_output', 'reverse_engineering_output']

        for i, field_name in enumerate(field_names):
            if result[i] is None:
                outputs[field_name] = None
            else:
                try:
                    outputs[field_name] = json.loads(result[i])
                except json.JSONDecodeError as e:
                    print(f"JSONDecodeError for {field_name} in patent {patent_number}: {e}")
                    print(f"Problematic data: {result[i][:200]}...")  # Show first 200 chars
                    # Try to fix common issues
                    try:
                        # Replace single quotes with double quotes for a quick fix
                        fixed_string = result[i].replace("'", '"')
                        outputs[field_name] = json.loads(fixed_string)
                        print(f"Successfully fixed {field_name} by replacing single quotes")
                    except:
                        print(f"Could not fix {field_name}, setting to None")
                        outputs[field_name] = None

        outputs['created_at'] = result[4]
        outputs['updated_at'] = result[5]
        return outputs

    return None


def check_existing_output(patent_number, output_type):
    """Check if a specific output already exists for a patent"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()

    cursor.execute(f'''
        SELECT {output_type} FROM output_persistence 
        WHERE patent_number = ? AND {output_type} IS NOT NULL
    ''', (patent_number,))

    result = cursor.fetchone()
    conn.close()

    return result is not None