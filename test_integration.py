#!/usr/bin/env python3
"""
Integration test to verify the complete agent flow works correctly
"""
import json
import asyncio
import sys
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

from models import InfringementRequest, ReverseEngineeringRequest, ClaimChartRequest, SpecSupportItem, KeyConceptItem

def create_sample_infringement_request():
    """Create a sample infringement request with complete spec support"""
    
    spec_support = [
        SpecSupportItem(
            key_feature="A user interface (100) for controlling a multichannel lighting unit (530) wherein each channel (B, G, R, FR) of the multichannel lighting unit comprises at least one light source (540) for emitting light having a channel-specific spectral composition",
            relevant_specification_1="Described herein is a user interface for controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable",
            relevant_specification_2="Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Each user interaction element 10, 20 includes a static scale 11, 21 and a dynamic scale 12, 22 depicted as a subrange overlaying the static scale",
            relevant_specification_3="The color associated with a user interaction elements does not necessarily directly associate with or relate to a channel of the multichannel lighting unit. Examples include a green color that is not directly associated with a green channel but is indirectly linked to the white channel"
        ),
        SpecSupportItem(
            key_feature="characterized in that the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element",
            relevant_specification_1="The dynamic range of a first of the at least two user interaction elements is automatically adapted when the present control value for a second of the at least two user interaction elements is changed using the selector of the second user interaction element",
            relevant_specification_2="A valid/executable range of control values for a particular user interaction element is determined based on technical characteristics of the multichannel lighting unit and operational characteristics such as selected control values for other user interaction elements",
            relevant_specification_3="In general, the dynamic range of a user interaction elements will be a subrange of the static range of that user interaction elements. The dynamic range excludes the end points of the static range"
        )
    ]
    
    return InfringementRequest(
        patent_number="EP3915337B1",
        priority_date="2019-01-21",
        assignees=["Signify Holding BV"],
        grant_date="2023-11-15",
        application_date="2020-01-17",
        expiration_date="2040-01-17",
        abstract="Dynamic user interface for controlling multichannel lighting with interdependent user interaction elements",
        classifications=["H05B47/175", "G06F3/0484"],
        inventors=["Roelf Melis VAN DER SLOOT", "Rob Franciscus Maria Van Elmpt"],
        patent_family=["EP3915337B1", "US11490493B2", "CN113287371B"],
        competitors=["EEMA Industries", "ETI Solid State Lighting", "Conrad Electronic"],
        keyconcepts=[
            KeyConceptItem(
                keyfeature="Dynamic user interface with interdependent controls",
                keyconcept="A user interface where the dynamic range of one control element is determined by the present value of another control element"
            )
        ],
        keyfeature_list=[
            "A user interface (100) for controlling a multichannel lighting unit (530)",
            "characterized in that the dynamic range of control values is a valid/executable range"
        ],
        spec_support=spec_support,
        forward_citation_assignees=["Shopify Inc.", "ヤマハ株式会社"],
        novelty_summary="Dynamic user interface for controlling multichannel lighting where the dynamic range of control values for one user interaction element is determined by the present control value of another user interaction element, with the dynamic scale depicted as a subrange overlaying the static scale"
    )

def simulate_infringement_response(request: InfringementRequest):
    """Simulate the expected infringement agent response structure"""
    
    return {
        "products": [
            {
                "patent_number": request.patent_number,
                "assignees": request.assignees,
                "priority_date": request.priority_date,
                "novelty_summary": request.novelty_summary,
                "keyfeature_list": request.keyfeature_list,
                "company": "Obsidian Control Systems",
                "model": "ONYX Software v4.2",
                "launch_date": "2019-07-16",
                "competitor_type": "Direct Competitor",
                "infringement_evidence_links": [
                    "https://obsidiancontrol.com/onyx",
                    "https://support.obsidiancontrol.com/Content/Color_Picker/Color_Picker_Window.htm"
                ],
                "spec_support": [{"key_feature": "simplified", "relevant_specification_1": "simplified spec"}]
            },
            {
                "patent_number": request.patent_number,
                "assignees": request.assignees,
                "priority_date": request.priority_date,
                "novelty_summary": request.novelty_summary,
                "keyfeature_list": request.keyfeature_list,
                "company": "ETC Inc",
                "model": "EOS Family Consoles",
                "launch_date": "2020-03-15",
                "competitor_type": "Direct Competitor",
                "infringement_evidence_links": [
                    "https://www.etcconnect.com/Products/Consoles/Eos-Family/",
                    "https://www.etcconnect.com/WorkArea/DownloadAsset.aspx?id=10737461461"
                ],
                "spec_support": [{"key_feature": "simplified", "relevant_specification_1": "simplified spec"}]
            }
        ],
        "spec_support": [item.model_dump() for item in request.spec_support]  # Complete original spec support preserved
    }

def test_reverse_engineering_request_creation(infringement_response):
    """Test creating reverse engineering request from infringement response"""
    
    # Convert response to ReverseEngineeringRequest
    reverse_eng_request = ReverseEngineeringRequest(
        products=[],  # Would be populated from infringement response products
        spec_support=[SpecSupportItem(**item) for item in infringement_response["spec_support"]]
    )
    
    # Verify spec support is preserved
    assert len(reverse_eng_request.spec_support) == 2
    assert reverse_eng_request.spec_support[0].key_feature.startswith("A user interface (100)")
    assert len(reverse_eng_request.spec_support[0].relevant_specification_1) > 100
    
    print("✓ Reverse engineering request preserves complete spec support")
    return reverse_eng_request

def simulate_reverse_engineering_response(reverse_eng_request, original_products):
    """Simulate reverse engineering agent response"""
    
    # Add more products while preserving complete spec support
    additional_products = [
        {
            "patent_number": original_products[0]["patent_number"],
            "assignees": original_products[0]["assignees"],
            "priority_date": original_products[0]["priority_date"],
            "novelty_summary": original_products[0]["novelty_summary"],
            "keyfeature_list": original_products[0]["keyfeature_list"],
            "company": "MA Lighting",
            "model": "grandMA3 Software",
            "launch_date": "2019-09-01",
            "competitor_type": "Direct Competitor",
            "infringement_evidence_links": [
                "https://www.malighting.com/products/control/grandma3/",
                "https://help2.malighting.com/Page/grandMA3/color_picker/en/1.7"
            ],
            "spec_support": [{"key_feature": "simplified", "relevant_specification_1": "simplified spec"}]
        }
    ]
    
    return {
        "products": original_products + additional_products,
        "spec_support": [item.model_dump() for item in reverse_eng_request.spec_support]  # Preserve complete spec support
    }

def test_claim_chart_request_creation(reverse_eng_response):
    """Test creating claim chart request from reverse engineering response"""
    
    claim_chart_request = ClaimChartRequest(
        products=[],  # Would be populated from reverse engineering response
        spec_support=[SpecSupportItem(**item) for item in reverse_eng_response["spec_support"]]
    )
    
    # Verify complete spec support is available for claim chart generation
    assert len(claim_chart_request.spec_support) == 2
    
    # Verify each spec support item has detailed specifications
    for spec_item in claim_chart_request.spec_support:
        assert len(spec_item.relevant_specification_1) > 100, "Specification 1 should be detailed"
        assert spec_item.relevant_specification_2 is not None, "Specification 2 should exist"
        assert len(spec_item.relevant_specification_2) > 50, "Specification 2 should be detailed"
        assert spec_item.relevant_specification_3 is not None, "Specification 3 should exist"
        assert len(spec_item.relevant_specification_3) > 50, "Specification 3 should be detailed"
    
    print("✓ Claim chart request has complete spec support for detailed mapping")
    
    # Verify that claim chart can map each product to each key feature
    num_products = len(reverse_eng_response["products"])
    num_key_features = len(claim_chart_request.spec_support)
    expected_mappings = num_products * num_key_features
    
    print(f"✓ Claim chart should generate {expected_mappings} mappings ({num_products} products × {num_key_features} key features)")
    
    return claim_chart_request

def main():
    """Run the complete integration test"""
    
    print("=== Integration Test: Complete Agent Flow ===\n")
    
    # Step 1: Create infringement request
    print("1. Creating infringement request with complete spec support...")
    infringement_request = create_sample_infringement_request()
    print(f"   ✓ Created request with {len(infringement_request.spec_support)} detailed spec support items")
    
    # Step 2: Simulate infringement response
    print("\n2. Simulating infringement agent response...")
    infringement_response = simulate_infringement_response(infringement_request)
    print(f"   ✓ Response contains {len(infringement_response['products'])} products")
    print(f"   ✓ Complete spec support preserved: {len(infringement_response['spec_support'])} items")
    
    # Step 3: Create reverse engineering request
    print("\n3. Creating reverse engineering request...")
    reverse_eng_request = test_reverse_engineering_request_creation(infringement_response)
    
    # Step 4: Simulate reverse engineering response
    print("\n4. Simulating reverse engineering agent response...")
    reverse_eng_response = simulate_reverse_engineering_response(reverse_eng_request, infringement_response["products"])
    print(f"   ✓ Response contains {len(reverse_eng_response['products'])} products (added more)")
    print(f"   ✓ Complete spec support preserved: {len(reverse_eng_response['spec_support'])} items")
    
    # Step 5: Create claim chart request
    print("\n5. Creating claim chart request...")
    claim_chart_request = test_claim_chart_request_creation(reverse_eng_response)
    
    # Step 6: Verify complete data flow
    print("\n6. Verifying complete data flow...")
    
    # Verify spec support consistency throughout the flow
    original_spec_count = len(infringement_request.spec_support)
    final_spec_count = len(claim_chart_request.spec_support)
    
    assert original_spec_count == final_spec_count, f"Spec support count mismatch: {original_spec_count} → {final_spec_count}"
    
    # Verify spec support content is preserved
    original_first_feature = infringement_request.spec_support[0].key_feature
    final_first_feature = claim_chart_request.spec_support[0].key_feature
    
    assert original_first_feature == final_first_feature, "Spec support content not preserved"
    
    print("   ✓ Spec support count preserved throughout flow")
    print("   ✓ Spec support content preserved throughout flow")
    print("   ✓ Products accumulated correctly through agents")
    
    print(f"\n🎉 Integration test passed!")
    print(f"   • Final product count: {len(reverse_eng_response['products'])}")
    print(f"   • Complete spec support items: {len(claim_chart_request.spec_support)}")
    print(f"   • Expected claim chart mappings: {len(reverse_eng_response['products']) * len(claim_chart_request.spec_support)}")
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ All integration tests passed! The agent flow is properly configured.")
    else:
        print("\n❌ Integration tests failed!")
        sys.exit(1)
