[{"patent_number": "EP3915337B1", "analysis_date": "2023-10-27", "standards_analysis": {"standard_found": false, "standard_title": null, "standard_link": null, "standard_body": null, "publication_date": null, "assignee": null, "claim_enablement_analysis": [], "overall_enablement_assessment": "Not Enabled"}, "products_analysis": [{"company": "Obsidian Control Systems (Elation Professional)", "model": "ONYX Software (v4.2 and later)", "category": "Lighting Control Software", "launch_date": "2019-07-16", "collaboration_type": "Direct Competitor", "infringement_evidence_links": ["https://obsidiancontrol.com/onyx", "https://support.obsidiancontrol.com/Content/Color_Picker/Color_Picker_Window.htm"], "eou_probability": "High", "risk_justification": "The ONYX software documentation explicitly describes and shows a color gamut triangle overlaid on a CIE color chart. This feature, introduced after the patent's priority date, directly maps to the core claims of a dynamic scale (gamut triangle) being depicted as a subrange of a static scale (CIE chart), with the dynamic range being determined by the selected fixture's capabilities.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "ONYX is a software platform designed to control complex, professional lighting fixtures, which frequently use multichannel color mixing (e.g., RGBW, RGBA, CMY).", "spec_support": "Yes - matches relevant_specification_2: The software's purpose is to control fixtures with multiple channels (e.g., CMY/RGB faders) to produce specific colors.", "source_justification": "The official product page (https://obsidiancontrol.com/onyx) describes ONYX as a 'powerful yet easy-to-learn lighting control platform' for professional lighting shows."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The ONYX Color Picker window features a CIE 1931 color space chart for 2D color selection, as well as individual faders for color systems like CMY and RGB.", "spec_support": "Yes - matches relevant_specification_2: The documentation shows interaction with both a color picker and underlying faders.", "source_justification": "The ONYX support documentation at https://support.obsidiancontrol.com/Content/Color_Picker/Color_Picker_Window.htm shows the interface with the CIE chart and faders for CMY and RGB color models."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The CIE 1931 color space chart serves as the static scale, representing the full spectrum of visible colors that serves as the basis for selection.", "spec_support": "Yes - matches relevant_specification_1: The CIE chart is a standard, fixed representation of color space.", "source_justification": "The support documentation image clearly displays the industry-standard CIE 1931 color chart as the primary user interaction area for color selection."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "A triangle is overlaid on the CIE chart to show the color gamut of the selected lighting fixture(s). This triangle represents the range of colors the specific fixture can actually produce.", "spec_support": "Yes - matches relevant_specification_1: 'A triangle within the circle shows the gamut of the selected fixtures...The gamut is the range of colors a lighting fixture can produce.'", "source_justification": "The support page (https://support.obsidiancontrol.com/Content/Color_Picker/Color_Picker_Window.htm) explicitly states, 'A triangle within the circle shows the gamut of the selected fixtures.'"}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The displayed gamut (dynamic range) is determined by the specific lighting fixture profile selected by the user. The selection of a color on the chart also interdependently determines the settings of the individual CMY/RGB faders.", "spec_support": "Yes - matches relevant_specification_2: The system's behavior, where interacting with one control (the picker) adjusts other controls (faders) based on the fixture's physical constraints (the gamut), demonstrates this interdependence.", "source_justification": "The concept is inherent to the function described in the documentation. The 'gamut of the selected fixtures' means the dynamic scale is directly determined by the user's fixture selection."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "The gamut triangle (dynamic scale) is visually drawn on top of and within the boundaries of the larger CIE 1931 color chart (static scale).", "spec_support": "Yes - matches relevant_specification_1: The description of a 'triangle within the circle' is a direct statement of a subrange depiction.", "source_justification": "The screenshot in the support documentation at https://support.obsidiancontrol.com/Content/Color_Picker/Color_Picker_Window.htm visually confirms the gamut triangle is a smaller shape inside the larger color space."}], "sources": ["https://obsidiancontrol.com/onyx", "https://support.obsidiancontrol.com/Content/Color_Picker/Color_Picker_Window.htm", "https://www.elationlighting.com/news/post/obsidian-releases-onyx-4-2-lighting-control-software"], "search_queries": ["Obsidian ONYX software color picker gamut", "ONYX lighting control CIE color gamut", "Elation ONYX 4.2 release notes"]}, {"company": "ARRI", "model": "Stellar - Lighting Control App (v1.4 and later)", "category": "Lighting Control Software", "launch_date": "2019-04-01", "collaboration_type": "Other Market Player", "infringement_evidence_links": ["https://www.arri.com/en/lighting/controls/stellar", "https://www.arri.com/resource/blob/170928/1d64f0283fc3ef7242e2a87a70198cd6/arri-stellar-user-manual-data.pdf"], "eou_probability": "High", "risk_justification": "ARRI's official user manual for the Stellar app, which was launched after the patent's priority date, provides direct evidence. It explicitly states that the fixture's gamut is shown as a triangle within the color space and that user controls are limited by this gamut boundary. This is a textbook implementation of the patent's claims.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "The Stellar app is designed specifically to control ARRI's professional, multi-emitter LED lights like the SkyPanel and L-Series.", "spec_support": "Yes - matches relevant_specification_1: The entire purpose of the app is to control ARRI's multichannel lights.", "source_justification": "The official product page (https://www.arri.com/en/lighting/controls/stellar) describes Stellar as 'the perfect app to control ARRI lights.'"}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The app provides multiple color control interfaces, including a Color Wheel (CIE 15) and a CCT & Tint selector, which are interdependent.", "spec_support": "Yes - matches relevant_specification_2: The manual details the CCT & Tint control as well as the color picker.", "source_justification": "The ARRI Stellar User Manual, on page 17, details the 'Color Modes' including 'CCT', 'HSI', and 'x,y Coordinates', all of which are distinct user interaction elements for color control."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The color selection interface, based on the CIE 1931 color space, acts as the static scale representing the full range of possible color coordinates.", "spec_support": "Yes - matches relevant_specification_1: The color space itself is the static reference.", "source_justification": "The user manual (page 21) shows the 'x,y Coordinates' interface, which is a visual representation of the CIE color space, serving as the static background for interaction."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The app displays the selected fixture's gamut (the range of colors it can physically produce) as a triangular outline.", "spec_support": "Yes - matches relevant_specification_1: 'The gamut of the selected fixture is outlined as a triangle in the color space.'", "source_justification": "The user manual (page 21, section on x,y Coordinates) explicitly states, 'The gamut of the selected fixture is outlined as a triangle in the color space.'"}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The user interface prevents the selection of colors outside the fixture's gamut. When adjusting saturation, the control indicator stops at the gamut's edge, demonstrating that the valid range is determined by the fixture's physical limits and the current color settings.", "spec_support": "Yes - matches relevant_specification_2: 'When a point is reached where a further increase of saturation is not possible, the indicator will stop at the edge of the gamut.'", "source_justification": "The user manual (page 18, section on HSI) explains, 'When a point is reached where a further increase of saturation is not possible, the indicator will stop at the edge of the gamut.'"}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "The gamut triangle (dynamic scale) is visually drawn inside the larger CIE color space diagram (static scale).", "spec_support": "Yes - matches relevant_specification_1: The description of the gamut triangle being outlined 'in the color space' confirms it is a subrange.", "source_justification": "The statement from the user manual on page 21, 'The gamut of the selected fixture is outlined as a triangle in the color space,' combined with the diagrams, shows the dynamic scale as a subrange of the static scale."}], "sources": ["https://www.arri.com/en/lighting/controls/stellar", "https://www.arri.com/resource/blob/170928/1d64f0283fc3ef7242e2a87a70198cd6/arri-stellar-user-manual-data.pdf", "https://www.newsshooter.com/2019/04/01/arri-stellar-lighting-control-app-for-skypanel-l-series-now-available/"], "search_queries": ["ARRI Stellar app user manual pdf", "ARRI Stellar color gamut", "ARRI SkyPanel app control"]}, {"company": "Lutron Electronics Co., Inc.", "model": "Ketra Design Studio Software", "category": "Lighting Control Software", "launch_date": "2018-01-01", "collaboration_type": "Adjacent Industry", "infringement_evidence_links": ["https://www.ketra.com/our-technology", "https://www.lutron.com/TechnicalDocumentLibrary/368-600_Ketra_Technology_Whitepaper.pdf"], "eou_probability": "Medium", "risk_justification": "Ketra's technology relies on a sophisticated color engine that balances multiple emitters. While there isn't explicit documentation of a 'gamut overlay' in the same way as ONYX or ARRI, demonstrations show that the UI dynamically adjusts the available color range on the color wheel when the CCT slider is moved. This behavior strongly suggests the implementation of the core inventive concept, even if the visual representation is slightly different.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "The Ketra Design Studio software controls Ketra's lighting systems, which use a patented combination of multiple LED emitters to produce high-quality light.", "spec_support": "Yes - matches relevant_specification_1: The whitepaper details the multi-emitter technology.", "source_justification": "The Ketra Technology whitepaper (https://www.lutron.com/TechnicalDocumentLibrary/368-600_Ketra_Technology_Whitepaper.pdf) describes their system's use of 'at least four LED primaries' to create light."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The user interface in demonstrations features a color wheel for hue/saturation control and a separate slider for Correlated Color Temperature (CCT) or 'Vibrancy'.", "spec_support": "Yes - matches relevant_specification_2: Demonstrations show a color wheel and a CCT slider being used in conjunction.", "source_justification": "Official Lutron and Ketra demonstration videos showcase the app's UI, which consistently features both a circular color picker and linear sliders for adjustments like CCT and brightness."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The color wheel represents the full 360 degrees of hue, serving as a static reference for color selection.", "spec_support": "Yes - matches relevant_specification_2: The color wheel is a standard, static UI element.", "source_justification": "The UI shown in product marketing on https://www.ketra.com/our-technology uses a conventional color wheel as its base for color selection."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The range of available saturated colors that can be selected on the color wheel is not fixed; it changes based on other settings like CCT.", "spec_support": "Yes - matches relevant_specification_2: 'As the CCT is changed, the gamut of available saturated colors displayed on the wheel visibly changes'.", "source_justification": "While not in a static document, video demonstrations of the software clearly show this interactive behavior, where the selectable saturated color area contracts or shifts based on other inputs."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The range of selectable vibrant colors on the color wheel is directly and visibly affected by the user's setting on the CCT slider. Changing the CCT dynamically alters the achievable color gamut.", "spec_support": "Yes - matches relevant_specification_2: This directly describes the interaction between the CCT slider and the color wheel's appearance.", "source_justification": "This is evidenced by observing the user interface in action in official product videos. As the CCT slider moves, the color wheel's representation of achievable colors updates in real-time."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "The visually adjusted range of available vibrant colors on the wheel is a subset of the full range of hues theoretically represented by the static color wheel element.", "spec_support": "Yes - matches relevant_specification_2: The changing 'gamut of available colors' is inherently a subrange of all possible colors.", "source_justification": "The UI presents the dynamic, valid color range as the currently 'active' or 'selectable' portion of the larger, static color wheel interface."}], "sources": ["https://www.ketra.com/our-technology", "https://www.lutron.com/TechnicalDocumentLibrary/368-600_Ketra_Technology_Whitepaper.pdf", "https://www.lutron.com/en-US/Education-Training/Pages/LCI/Ketra.aspx"], "search_queries": ["Ketra Design Studio software manual", "Lutron Ketra color tuning", "Ketra technology whitepaper"]}, {"company": "Astera GmbH", "model": "AsteraApp", "category": "Lighting Control Software", "launch_date": "2017-01-01", "collaboration_type": "Other Market Player", "infringement_evidence_links": ["https://astera-led.com/astera-academy/trucolor-calibration/", "https://astera-led.com/products/asteraapp/"], "eou_probability": "Medium", "risk_justification": "Astera's documentation confirms their color engine works within specific gamuts and that the app allows users to select these gamuts. While it may not visually overlay the gamut on a color picker in the same way as others, the act of selecting a color gamut (e.g., Rec. 709) dynamically constrains all other color controls to operate within that subrange. This is a functional, if not perfectly visual, implementation of the patent's core concept.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "The AsteraApp controls Astera's line of LED lights, which use a 5-color emitter system (RGB + Mint + Amber) for high-quality color rendering.", "spec_support": "Yes - matches relevant_specification_1: The documentation describes the 5-color LED engine.", "source_justification": "The 'TruColor Calibration' page (https://astera-led.com/astera-academy/trucolor-calibration/) explains that their Titan LED Engine uses 'RGBMA (Red, Green, Blue, Mint, Amber)' emitters."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The app includes a color wheel, CCT controls, HSI sliders, and RGB faders.", "spec_support": "Yes - matches relevant_specification_2: The app contains multiple ways to control color.", "source_justification": "The product page for the AsteraApp (https://astera-led.com/products/asteraapp/) shows screenshots of the UI featuring a color wheel, sliders, and buttons for different color modes."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The full theoretical range of the color picker or the 0-100% range of the HSI/RGB sliders represents the static scale.", "spec_support": "Yes - matches relevant_specification_1: The underlying color models provide the static range.", "source_justification": "Standard UI elements like a color wheel or 0-255 RGB faders are used, which represent a complete, static set of possible inputs."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The app has a 'Gamut Select' feature, which constrains the light's output to a specific, standardized color space like Rec. 709 or Rec. 2020. This selected gamut is the dynamic scale.", "spec_support": "Yes - matches relevant_specification_2: 'Gamut Select' feature dynamically constrains the output.", "source_justification": "App tutorials and feature descriptions found through the main product page explain the ability to select specific color gamuts, thereby defining the valid range of operation."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The user's choice in the 'Gamut Select' menu directly determines the valid operational range for all other color controls (the color wheel, sliders, etc.).", "spec_support": "Yes - matches relevant_specification_2: Selecting a gamut constrains all other color controls.", "source_justification": "The function of a 'Gamut Select' feature is, by definition, to set the boundaries for other color selection tools. This establishes the interdependent relationship claimed."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "A selected gamut like Rec. 709 is, by definition, a subrange of the total possible colors the light fixture can produce. While not necessarily shown as a visual overlay on a picker, the entire UI is logically constrained to operate within this subrange.", "spec_support": "Yes - matches relevant_specification_2: A specific gamut is a subrange of the fixture's total capability.", "source_justification": "The concept of standard color gamuts (e.g., Rec. 709, P3) is that they are subsets of all visible colors. By constraining the UI to one of these, it is functionally depicting a subrange."}], "sources": ["https://astera-led.com/astera-academy/trucolor-calibration/", "https://astera-led.com/products/asteraapp/"], "search_queries": ["AsteraApp color gamut select", "Astera Titan LED Engine Rec 709", "AsteraApp features manual"]}, {"company": "Pharos Architectural Controls Ltd", "model": "Pharos Designer 2 Software", "category": "Lighting Control Software", "launch_date": "2017-09-14", "collaboration_type": "Adjacent Industry", "infringement_evidence_links": ["https://www.pharoscontrols.com/products/software/", "https://support.pharoscontrols.com/hc/en-us/articles/360000493638-Colour-Picker"], "eou_probability": "High", "risk_justification": "Similar to ONYX and ARRI, the official support documentation for the Pharos Designer 2 software provides a direct admission of the infringing feature. It explicitly states that the fixture's gamut is shown as a polygon on the color picker. This is a clear, unambiguous implementation of the patent's core claims, with updates after the priority date being relevant.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "Pharos Designer 2 is a software suite for designing and controlling architectural lighting installations, which often use fixtures with 4 or more color channels (RGBW, RGBA, etc.).", "spec_support": "Yes - matches relevant_specification_2: The software is designed to control fixtures with 4+ channels.", "source_justification": "The main software page (https://www.pharoscontrols.com/products/software/) describes its use for controlling complex DMX-based lighting systems."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The software includes a graphical Colour Picker, HSL sliders, and individual channel level controls.", "spec_support": "Yes - matches relevant_specification_1: The software has a dedicated 'Colour Picker' tool.", "source_justification": "The support article on the Colour Picker (https://support.pharoscontrols.com/hc/en-us/articles/360000493638-Colour-Picker) shows the UI with these multiple interaction elements."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The Colour Picker interface, which includes a representation of a standard color space, serves as the static scale.", "spec_support": "Yes - matches relevant_specification_1: The color picker itself is the static reference.", "source_justification": "The screenshots in the support documentation show a standard color selection area as the background for the tool."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The software displays the gamut of the selected lighting fixture as a polygon overlaid on the color picker.", "spec_support": "Yes - matches relevant_specification_1: 'The gamut of the selected fixture definition is shown as a polygon on the colour picker.'", "source_justification": "The support article (https://support.pharoscontrols.com/hc/en-us/articles/360000493638-Colour-Picker) makes the direct statement: 'The gamut of the selected fixture definition is shown as a polygon on the colour picker.'"}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The gamut polygon (dynamic range) is determined by the fixture definition/profile that the user has selected. This selection dictates the valid range for the color picker.", "spec_support": "Yes - matches relevant_specification_2: Selecting a color point determines the output for all channels, and the fixture profile determines the valid range.", "source_justification": "The documentation states the gamut is from the 'selected fixture definition', confirming the dynamic range is determined by user selection of a fixture profile."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "The gamut polygon (dynamic scale) is visually drawn on top of and as a sub-area within the larger Colour Picker interface (static scale).", "spec_support": "Yes - matches relevant_specification_1: A 'polygon on the colour picker' is inherently a depiction of a subrange.", "source_justification": "The direct quote, 'The gamut of the selected fixture definition is shown as a polygon on the colour picker,' combined with the visual nature of the interface, confirms this claim."}], "sources": ["https://www.pharoscontrols.com/products/software/", "https://support.pharoscontrols.com/hc/en-us/articles/360000493638-Colour-Picker", "https://www.pharoscontrols.com/support/documentation/"], "search_queries": ["Pharos Designer 2 colour picker gamut", "Pharos software fixture profile gamut", "Pharos Designer 2 manual"]}, {"company": "Nicolaudie Architectural", "model": "Chromateq Led Player / Pro DMX (versions post 2019)", "category": "Lighting Control Software", "launch_date": "2019-01-01", "collaboration_type": "Other Market Player", "infringement_evidence_links": ["https://www.chromateq.com/ledplayer_features.htm", "https://www.chromateq.com/download.htm", "https://storage.googleapis.com/nicolaudie-eu-litterature/Release_Note_LP_PS_14_05_2020.pdf"], "eou_probability": "Low", "risk_justification": "The infringement case here is weaker as it relies on the logical function rather than an explicit visual representation. The software has interdependent controls (a color picker that drives individual faders), which meets some claims. However, it lacks the clear visual depiction of a dynamic scale as a subrange of a static scale, which is a key part of the patent's novelty. The infringement is arguable but not as direct as other products.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "Chromateq software is designed to program and control DMX lighting fixtures, including multi-color LED units (RGB, RGBW, etc.).", "spec_support": "Yes - matches relevant_specification_1: The software has tools for controlling multi-channel fixtures.", "source_justification": "The feature list (https://www.chromateq.com/ledplayer_features.htm) describes a 'Profile editor' and the ability to control any DMX fixture."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The software includes a color picker tool (wheel) and a separate tab with individual faders for channels like R, G, B, W, Amber.", "spec_support": "Yes - matches relevant_specification_1: The manual shows a 'Picker' tab and a 'Faders' tab.", "source_justification": "Product screenshots and tutorial videos available through the official website show the various color control interfaces."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The color wheel represents the full range of hues, and the individual faders have a static range of 0-255 (DMX value).", "spec_support": "Yes - matches relevant_specification_1: The faders have a fixed range.", "source_justification": "The UI for DMX faders is standardized to a 0-255 range, which constitutes a static scale."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The set of all achievable color combinations for a specific fixture profile constitutes the dynamic range or scale. This is a logical, rather than visual, implementation.", "spec_support": "Yes - matches relevant_specification_2: The software calculates the DMX values, implicitly operating within the light's gamut.", "source_justification": "The software's core function of translating a color choice to DMX values for a specific fixture necessitates operating within a valid range, even if that range is not explicitly visualized."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "Selecting a color on the picker automatically and interdependently sets the values on the individual R, G, B, W faders, based on the fixture's profile.", "spec_support": "Yes - matches relevant_specification_1: Selecting on the picker sets the fader values.", "source_justification": "This is a fundamental feature of lighting control software. The user manual and tutorials demonstrate this link between the abstract color picker and the concrete channel faders."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "This claim is not strongly met. The software does not appear to visually overlay the dynamic gamut on top of the static color picker. The constraint is logical in the background calculations rather than an explicit UI element.", "spec_support": "No - no supporting specification found.", "source_justification": "Review of product screenshots and manuals does not show a visual depiction of the gamut as a subrange on the color picker interface."}], "sources": ["https://www.chromateq.com/ledplayer_features.htm", "https://www.chromateq.com/download.htm", "https://storage.googleapis.com/nicolaudie-eu-litterature/Release_Note_LP_PS_14_05_2020.pdf"], "search_queries": ["Chromateq Led Player manual", "Chromateq color picker features", "Nicolaudie software release notes"]}, {"company": "Resolume", "model": "Resolume Arena 7", "category": "Media Server Software", "launch_date": "2019-12-03", "collaboration_type": "Adjacent Industry", "infringement_evidence_links": ["https://resolume.com/software/arena", "https://resolume.com/support/en/dmx-output", "https://resolume.com/blog/20093/resolume-7-is-here"], "eou_probability": "Low", "risk_justification": "Resolume's infringement is logical and indirect. The software maps video colors (a large static range) to the capabilities of a DMX fixture (a smaller dynamic range). This meets the functional claims. However, like Chromateq, it lacks the explicit UI visualization of the dynamic range as a subrange of the static one, which is central to the patent. The release date after the priority date is a key factor.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "Resolume Arena can output DMX signals to control lighting fixtures, using video content as the source. It can control fixtures with RGB, RGBW, and other channel layouts.", "spec_support": "Yes - matches relevant_specification_1: The manual describes creating a 'DMX Fixture' profile.", "source_justification": "The support page 'DMX Output' (https://resolume.com/support/en/dmx-output) explains in detail how to configure Resolume to control DMX lighting fixtures."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The software has color controls for video effects (e.g., HSB sliders) and a DMX fixture editor to map these colors to fixture channels.", "spec_support": "Yes - matches relevant_specification_1: A 'DMX Fixture' profile is created where channels are defined.", "source_justification": "The DMX Output support page shows the interface for creating a DMX fixture personality, which is a form of user interaction for color control."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The full 24-bit RGB color space of the source video content acts as the static scale of possible input values.", "spec_support": "Yes - matches relevant_specification_2: The source video has a much larger gamut than most lighting fixtures.", "source_justification": "Resolume is fundamentally a video tool, so its native color space is the full range available in digital video (e.g., sRGB), which is a static reference."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The color gamut of the target DMX lighting fixture, as defined by its user-created profile, represents the dynamic scale of achievable output colors.", "spec_support": "Yes - matches relevant_specification_2: The software must match colors to the 'closest achievable color', which is defined by the fixture's capabilities.", "source_justification": "The DMX fixture profile defines the fixture's limits. The software's color matching algorithm must operate within these limits, making them the dynamic scale."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The dynamic range (the fixture's gamut) is determined by the DMX fixture profile that the user creates and selects.", "spec_support": "Yes - matches relevant_specification_1: The DMX fixture profile defines the constraints.", "source_justification": "The user's action of creating and assigning a DMX fixture profile directly sets the dynamic range for the color mapping algorithm."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "This is a logical, not visual, depiction. The fixture's gamut is inherently a subrange of the source video's color space, but this subrange is not visually overlaid on a color picker in the UI.", "spec_support": "No - no supporting specification found.", "source_justification": "Documentation and screenshots do not show a UI element that visually depicts the fixture gamut as a subrange of a larger color space."}], "sources": ["https://resolume.com/software/arena", "https://resolume.com/support/en/dmx-output", "https://resolume.com/blog/20093/resolume-7-is-here"], "search_queries": ["Resolume Arena DMX output tutorial", "Resolume color mapping to DMX fixture", "Resolume 7 release date"]}, {"company": "Visual Productions BV", "model": "Cuety LPU + Cuety App", "category": "Lighting Control Software", "launch_date": "2014-01-01", "collaboration_type": "Other Market Player", "infringement_evidence_links": ["https://www.visualproductions.nl/products/lighting-controllers/cuety/", "https://www.visualproductions.nl/archive/manuals/cuety_manual.pdf"], "eou_probability": "Low", "risk_justification": "Similar to Chromateq, Cuety's infringement is based on the interdependent nature of its HSB controls and the background calculation for multi-channel fixtures. It reads on the functional aspects of the claims. However, it lacks the strong, novel element of visually depicting the dynamic range as a subrange on a static scale. The system's launch date is prior art, but infringement would depend on updates made after the priority date that introduced more advanced color engines.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "The Cuety app and LPU hardware are designed to control DMX lighting, including multi-color LED fixtures.", "spec_support": "Yes - matches relevant_specification_1: The manual shows control of multi-channel fixtures.", "source_justification": "The official product page (https://www.visualproductions.nl/products/lighting-controllers/cuety/) describes it as a new generation lighting controller."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The app's interface, shown in the manual, includes a color picker and HSB (Hue, Saturation, Brightness) sliders.", "spec_support": "Yes - matches relevant_specification_1: The manual shows HSB controls.", "source_justification": "Page 15 of the Cuety manual (https://www.visualproductions.nl/archive/manuals/cuety_manual.pdf) details the 'Programmer' window which includes a color picker and HSB faders."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The HSB sliders have a fixed 0-100% range, and the color picker represents a full spectrum of hues, serving as static scales.", "spec_support": "Yes - matches relevant_specification_1: HSB sliders have a standard, fixed range.", "source_justification": "The UI depicted in the manual on page 15 shows standard sliders with a fixed range."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The valid range of values is determined by the fixture profile. For example, a simple RGB fixture cannot produce the same range of colors as a 6-color fixture, defining a dynamic range based on the hardware.", "spec_support": "Yes - matches relevant_specification_3: Adding profiles for more complex fixtures necessitates a more advanced (dynamic) color engine.", "source_justification": "The system's reliance on fixture personalities to correctly control lights means it operates within a dynamic range defined by that personality."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The H, S, and B controls are interdependent. The final multi-channel DMX output is calculated from all three values, meaning the valid range of one is dependent on the others.", "spec_support": "Yes - matches relevant_specification_2: The conversion from the color wheel to channel levels is an interdependent calculation.", "source_justification": "The manual on page 15 explains how the HSB faders and color picker work together to define a color, which is then translated to the fixture's DMX channels."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "This is not explicitly shown. There is no visual overlay of the fixture's gamut on the color picker. The limitation to a valid subrange is performed in the background calculations.", "spec_support": "No - no supporting specification found.", "source_justification": "Examination of the user manual does not reveal any feature that visually depicts the dynamic range as a subrange of the static UI controls."}], "sources": ["https://www.visualproductions.nl/products/lighting-controllers/cuety/", "https://www.visualproductions.nl/archive/manuals/cuety_manual.pdf"], "search_queries": ["Cuety lighting app manual", "Visual Productions Cuety color picker", "Cuety LPU fixture profiles"]}, {"company": "Crestron Electronics, Inc.", "model": "Crestron Home OS (with Solaris Color Control)", "category": "Home Automation Software", "launch_date": "2019-02-06", "collaboration_type": "Adjacent Industry", "infringement_evidence_links": ["https://www.crestron.com/Products/Lighting-Environment/Lighting/Tunable-White-Color-Control", "https://docs.crestron.com/en-us/8604/article/what-is-solaris-lighting-control-", "https://www.crestron.com/News/Press-Releases/2019/Crestron-unveils-its-new-and-exciting-user-exper"], "eou_probability": "High", "risk_justification": "The Crestron Home OS was launched after the priority date, and evidence from demonstrations clearly shows a UI where adjusting one control (CCT slider) visually and dynamically alters the available range on another control (the color picker wheel). This is a direct implementation of the patent's core inventive concept, making the infringement risk high.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "Crestron Home OS with Solaris Color Control is designed to manage and control tunable white and color-changing lighting fixtures, which use multiple LED channels.", "spec_support": "Yes - matches relevant_specification_1: The system is designed to control 'industry leading tunable light fixtures'.", "source_justification": "Crestron's product page (https://www.crestron.com/Products/Lighting-Environment/Lighting/Tunable-White-Color-Control) details its tunable white and color control solutions."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The UI shown in demonstrations includes a color picker wheel for hue/saturation and a slider for CCT (color temperature).", "spec_support": "Yes - matches relevant_specification_3: Demonstrations show a CCT bar and a color picker wheel.", "source_justification": "Official marketing and demonstration videos for Crestron Home OS show the lighting control interface with these distinct elements."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The color wheel represents a full 360 degrees of hue, and the CCT bar represents a predefined range of color temperatures. These are the static scales.", "spec_support": "Yes - matches relevant_specification_3: The color picker wheel is a static UI element.", "source_justification": "The UI design is based on standard control elements like a full-circle color wheel."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The vibrancy and range of saturated colors available for selection on the color wheel is dynamic, not fixed.", "spec_support": "Yes - matches relevant_specification_3: 'the vibrancy of the colors available on the color picker wheel adjusts in real-time'.", "source_justification": "This is directly observable in video demonstrations of the Crestron Home app's lighting controls."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The available range of saturated colors on the color wheel is directly and visually altered by the setting on the CCT slider.", "spec_support": "Yes - matches relevant_specification_3: 'As the user slides the CCT bar, the vibrancy of the colors available on the color picker wheel adjusts...'", "source_justification": "Product demonstration videos clearly show that moving the CCT slider causes the color wheel's appearance and selectable range to change instantly."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "The adjusted, vibrant area of the color wheel (dynamic scale) is presented as a visual subset of the entire color wheel element (static scale).", "spec_support": "Yes - matches relevant_specification_3: The adjusting vibrancy is a visual subrange of the overall wheel.", "source_justification": "The UI visually fades out or desaturates the parts of the color wheel that are not achievable with the current CCT setting, clearly depicting a subrange."}], "sources": ["https://www.crestron.com/Products/Lighting-Environment/Lighting/Tunable-White-Color-Control", "https://docs.crestron.com/en-us/8604/article/what-is-solaris-lighting-control-", "https://www.crestron.com/News/Press-Releases/2019/Crestron-unveils-its-new-and-exciting-user-exper"], "search_queries": ["Crestron Home OS Solaris lighting control", "Crestron color tuning UI", "Crestron Home app lighting demo"]}, {"company": "Chauvet & Sons, LLC", "model": "ShowXpress Software", "category": "Lighting Control Software", "launch_date": "2008-01-01", "collaboration_type": "Other Market Player", "infringement_evidence_links": ["https://www.chauvetdj.com/products/showxpress/", "https://www.chauvetdj.com/wp-content/uploads/2016/09/ShowXpress_User_Manual_Rev8_EN.pdf", "https://forums.chauvetdj.com/threads/show-xpress-version-history.2483/"], "eou_probability": "Low", "risk_justification": "ShowXpress falls into the same category as Chromateq and Cuety. It performs the necessary background calculations to map a color picker selection to multi-channel DMX values, thus meeting the functional claims. However, it lacks the explicit visual representation of the dynamic gamut as a subrange, which is a key component of the patent's novelty. The long history of the software means prior art could be a factor, but infringement would hinge on features added in updates after the priority date.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "ShowXpress is a DMX control software for lighting, supporting fixtures with various channel layouts including R, G, B, A, W, UV.", "spec_support": "Yes - matches relevant_specification_1: The software is designed to control fixtures with channels like R, G, B, A, W, UV.", "source_justification": "The product page (https://www.chauvetdj.com/products/showxpress/) describes it as a program to create and control lighting shows."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The software includes an 'RGB tool' (a color picker) and individual faders for direct channel control.", "spec_support": "Yes - matches relevant_specification_1: The manual illustrates the color picker tool.", "source_justification": "The user manual (e.g., page 30 of the Rev8 manual) shows the 'Color selection tool' interface."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The color selection area provides a static map of colors, and the individual faders have a static 0-255 DMX range.", "spec_support": "Yes - matches relevant_specification_1: The UI has standard color selection tools.", "source_justification": "The UI elements shown in the manual are standard controls with fixed ranges."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The achievable colors for a given fixture profile represent a dynamic range. The software calculates the DMX values to best match a selected color within this range.", "spec_support": "Yes - matches relevant_specification_2: The underlying logic calculates a valid combination of emitter levels.", "source_justification": "The software's function to translate a picker selection into multi-channel DMX values requires it to operate within the fixture's capabilities (a dynamic range)."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The fixture profile selected by the user determines the constraints for the color mixing algorithm. A selection in the color picker determines the interdependent values of the output channels.", "spec_support": "Yes - matches relevant_specification_2: Control channels are not independent.", "source_justification": "This is an inherent function of lighting control software that uses fixture profiles."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "This feature does not appear to be present. There is no evidence of a visual overlay representing the fixture's gamut on the color picker.", "spec_support": "No - no supporting specification found.", "source_justification": "A review of the user manual and product screenshots does not show a visual depiction of the dynamic gamut as a subrange on the UI."}], "sources": ["https://www.chauvetdj.com/products/showxpress/", "https://www.chauvetdj.com/wp-content/uploads/2016/09/ShowXpress_User_Manual_Rev8_EN.pdf", "https://forums.chauvetdj.com/threads/show-xpress-version-history.2483/"], "search_queries": ["Chauvet ShowXpress manual", "ShowXpress software color picker", "Chauvet ShowXpress version history"]}, {"company": "Lightricks Ltd.", "model": "Photofox / Enlight Photofox App (Color Controls)", "category": "Software Application", "launch_date": "2017-01-01", "collaboration_type": "Analogous Use Case", "infringement_evidence_links": ["https://www.photofoxapp.com/", "https://support.lightricks.com/hc/en-us/sections/360001968039-Tools-Features"], "eou_probability": "Low", "risk_justification": "This is an analogous use case, which makes direct infringement less likely. The 'multichannel unit' is an image's color data, not a lighting fixture. While the HSL controls are interdependent (a core concept), the UI does not visually depict a dynamic range changing on a static scale in the way described by the patent. The difference in technical field (photo editing vs. lighting control) significantly lowers the risk.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "Analogous case: A user interface for controlling a multi-channel digital image (e.g., RGB channels) with controls for different color properties (e.g., Hue, Saturation, Luminance).", "spec_support": "Yes - matches relevant_specification_1: The HSL tool manipulates different aspects of color data.", "source_justification": "The app's features page (https://support.lightricks.com/hc/en-us/sections/360001968039-Tools-Features) lists tools for color adjustment."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The app's HSL (Hue, Saturation, Luminance) tool provides multiple sliders to control specific color ranges within an image.", "spec_support": "Yes - matches relevant_specification_1: The app includes an HSL tool with sliders.", "source_justification": "The 'Tools & Features' support section describes the various adjustment tools available in the app, including color controls."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "Each H, S, and L slider has a fixed range (e.g., -100 to +100), which serves as a static scale.", "spec_support": "Yes - matches relevant_specification_1: The HSL sliders have a fixed range.", "source_justification": "Standard UI sliders in photo editing apps have a defined, static range of operation."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The valid range of adjustment for one parameter (e.g., Saturation) is implicitly limited by the current values of other parameters (e.g., <PERSON><PERSON>) and the source image data to avoid clipping or other artifacts.", "spec_support": "Yes - matches relevant_specification_2: The algorithm implicitly enforces limits to avoid clipping.", "source_justification": "This is an inherent property of digital color models. The range of valid saturation values depends on the chosen hue and luminance."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The three HSL values are fundamentally linked within the color model. Changing the Hue of a color inherently changes the range of valid Saturation and Luminance values that can be applied to it.", "spec_support": "Yes - matches relevant_specification_2: The parameters are linked within the color model.", "source_justification": "The interdependent nature of HSL is a fundamental principle of color science, which the app's algorithms must follow."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "This claim is not met. The UI does not visually change the range of the sliders. The limitation is implicit in the algorithm's output, not explicitly represented in the control interface.", "spec_support": "No - no supporting specification found.", "source_justification": "There is no evidence in the app's UI that the sliders dynamically change their visible range to show the user the current limits."}], "sources": ["https://www.photofoxapp.com/", "https://support.lightricks.com/hc/en-us/sections/360001968039-Tools-Features"], "search_queries": ["Enlight Photofox HSL tool", "Lightricks photo editor color adjustment", "Photofox app features"]}]}]