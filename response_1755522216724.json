{"novelty_output": {"patent_number": "EP3915337B1", "title": "Dynamic user interface", "abstract": "Abstract not found.", "assignees": ["Signify Holding BV"], "inventors": ["<PERSON><PERSON> VAN DER SLOOT", "Rob <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> VRIES"], "application_date": "2020-01-17", "priority_date": "2019-01-21", "grant_date": "2023-11-15", "expiration_date": "2040-01-17", "patent_family": ["ES2969610T3", "EP3915337B1", "CN113287371B", "WO2020152058A1", "US11490493B2"], "classifications": [], "forward_citation_assignees": ["Shopify Inc.", "ヤマハ株式会社"], "competitors": ["EEMA Industries", "ETI Solid State Lighting", "<PERSON>", "<PERSON><PERSON>", "Seoul Semiconductor", "Luminex International"], "keyconcepts": [{"keyfeature": "A user interface (100) for controlling a multichannel lighting unit (530) wherein each channel (B, G, R, FR) of the multichannel lighting unit comprises at least one light source (540) for emitting light having a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the user interface comprising:- at least two user interaction elements (10, 20), each user interaction element associated with a color producible with the multichannel lighting unit, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising:- a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit,- a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element,- a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element,", "keyconcept": "A user interface (100) for controlling a multichannel lighting unit (530) where each channel (B, G, R, FR) has at least one light source (540) emitting light with a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), where spectral compositions differ between channels and light output from each channel is individually controllable. The user interface includes at least two user interaction elements (10, 20), each associated with a producible color, and at least one of these colors does not have a directly corresponding channel. Each user interaction element comprises: a static scale (11, 21) representing a static range of control values for controlling light output based on the associated color, with the range determined by the minimum and maximum light output in that color; a dynamic scale (12, 22) representing a dynamic range of control values for controlling light output based on the associated color; and a selector (13, 23) adjustable by a user to select a present control value for the light output based on the associated color. The dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale."}, {"keyfeature": "characterized in thatthe dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, andthe dynamic scale is depicted as a subrange of the static scale and overlaying said static scale.", "keyconcept": "The dynamic range of control values for a user interaction element is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, specifically within the context of controlling a multichannel lighting unit where each user interaction element is associated with a color producible by the unit."}, {"keyfeature": "A method of controlling a multichannel lighting unit (530) wherein each channel (B, G, R, FR) of the multichannel lighting unit comprises at least one light source (540) for emitting light having a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising:-", "keyconcept": "A method of controlling a multichannel lighting unit (530) via a user interface (100) with at least two user interaction elements (10, 20), where each channel (B, G, R, FR) has a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR) and individually controllable light output. The method involves providing user interaction elements, each with a static scale (11, 21) and a dynamic scale (12, 22) for controlling light output in respect of an associated color, where the dynamic range is a valid/executable range determined by a present control value selected from another user interaction element, and the dynamic scale is depicted as a subrange of and overlaying the static scale."}, {"keyfeature": "providing at least two user interaction elements (10, 11), each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising:-", "keyconcept": "providing at least two user interaction elements (10, 11), each user interaction element associated with a color producible with the multichannel lighting unit, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising a static scale (11, 21), a dynamic scale (12, 22), and a selector (13, 23)"}, {"keyfeature": "a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit,-", "keyconcept": "a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit (530) in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, and where the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit"}, {"keyfeature": "a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element,-", "keyconcept": "a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale"}, {"keyfeature": "a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element;-", "keyconcept": "a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element, where the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale."}, {"keyfeature": "selecting, using the selector of a first of the at least two user interaction elements, a present control value for the color associated with the first user interaction element;-", "keyconcept": "selecting, using the selector (13, 23) of a first of the at least two user interaction elements (10, 20), a present control value for the color associated with the first user interaction element, where the color is producible with the multichannel lighting unit (530) and may not have a directly corresponding channel."}, {"keyfeature": "adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and-", "keyconcept": "adapting the dynamic scale of a second user interaction element, based on the present control value selected in respect of the first user interaction element, where the dynamic scale represents a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, and the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale"}, {"keyfeature": "controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element,characterized in thatthe dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, andthe dynamic scale is depicted as a subrange of the static scale and overlaying said static scale.", "keyconcept": "controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element, where the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, within a user interface for controlling a multichannel lighting unit where each channel has a channel-specific spectral composition and light output is individually controllable, and at least two user interaction elements are provided, each associated with a color producible by the lighting unit, with at least one color not having a directly corresponding channel, and each user interaction element includes a static scale representing a static range of control values (determined by minimum and maximum light output in the associated color), a dynamic scale representing a dynamic range of control values, and a selector for selecting a present control value."}], "keyfeature_list": ["A user interface (100) for controlling a multichannel lighting unit (530) wherein each channel (B, G, R, FR) of the multichannel lighting unit comprises at least one light source (540) for emitting light having a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the user interface comprising:- at least two user interaction elements (10, 20), each user interaction element associated with a color producible with the multichannel lighting unit, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising:- a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit,- a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element,- a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element,", "characterized in thatthe dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, andthe dynamic scale is depicted as a subrange of the static scale and overlaying said static scale.", "A method of controlling a multichannel lighting unit (530) wherein each channel (B, G, R, FR) of the multichannel lighting unit comprises at least one light source (540) for emitting light having a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising:-", "providing at least two user interaction elements (10, 11), each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising:-", "a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit,-", "a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element,-", "a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element;-", "selecting, using the selector of a first of the at least two user interaction elements, a present control value for the color associated with the first user interaction element;-", "adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and-", "controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element,characterized in thatthe dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, andthe dynamic scale is depicted as a subrange of the static scale and overlaying said static scale."], "spec_support": [{"key_feature": "A user interface (100) for controlling a multichannel lighting unit (530) wherein each channel (B, G, R, FR) of the multichannel lighting unit comprises at least one light source (540) for emitting light having a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the user interface comprising:- at least two user interaction elements (10, 20), each user interaction element associated with a color producible with the multichannel lighting unit, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising:- a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit,- a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element,- a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element,", "relevant_specification_1": "Described herein is a user interface for controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the user interface comprising: at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element.", "relevant_specification_2": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color.", "relevant_specification_3": "The color associated with a user interaction elements does not necessarily directly associate with or relate to a channel of the multichannel lighting unit. Examples include a green color that is not directly associated with a green channel of the multichannel lighting unit but is indirectly linked to the white channel because the white channel will also provide green light emission, or a far-red color that is not directly associated with a far-red channel of the multichannel lighting unit but is indirectly linked to the red channel which also provides some far-red light emission. Therefore, according to the invention, the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit."}, {"key_feature": "characterized in thatthe dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, andthe dynamic scale is depicted as a subrange of the static scale and overlaying said static scale.", "relevant_specification_1": "Described herein is a user interface for controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the user interface comprising: at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element.", "relevant_specification_2": "The dynamic range of a first of the at least two user interaction elements is automatically adapted when the present control value for a second of the at least two user interaction elements is changed using the selector of the second of the at least two user interaction elements. A valid/executable range of control values for a particular user interaction element is determined based on (1) technical characteristics of the multichannel lighting unit such the number and spectral composition of the different channels of the multichannel lighting unit and maximum power consumption of the lighting unit, and (2) operational characteristics of the multichannel lighting unit such as selected control values for other user interaction elements. Control values may include luminous/radiant power or luminous/radiant flux, luminous/radiant intensity, illuminance/irradiance or any other value representing an amount of light/radiation. In general, the dynamic range of a user interaction elements will be a subrange of the static range of that user interaction elements. In embodiments, the dynamic range excludes the end points of the static range, more specifically a minimum control value of the dynamic range of a user interaction element is larger than a minimum control value of the static range for that user interaction element. In embodiment, the selector may overlay the dynamic scale, which itself overlays the static scale. Some or all of the static scale, dynamic scale and selector may be semitransparent such that they maintain visible to the user or operator of the user interface even when overlaid.", "relevant_specification_3": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color."}, {"key_feature": "A method of controlling a multichannel lighting unit (530) wherein each channel (B, G, R, FR) of the multichannel lighting unit comprises at least one light source (540) for emitting light having a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising:-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "The dynamic range of a first of the at least two user interaction elements is automatically adapted when the present control value for a second of the at least two user interaction elements is changed using the selector of the second of the at least two user interaction elements. A valid/executable range of control values for a particular user interaction element is determined based on (1) technical characteristics of the multichannel lighting unit such the number and spectral composition of the different channels of the multichannel lighting unit and maximum power consumption of the lighting unit, and (2) operational characteristics of the multichannel lighting unit such as selected control values for other user interaction elements. Control values may include luminous/radiant power or luminous/radiant flux, luminous/radiant intensity, illuminance/irradiance or any other value representing an amount of light/radiation. In general, the dynamic range of a user interaction elements will be a subrange of the static range of that user interaction elements. In embodiments, the dynamic range excludes the end points of the static range, more specifically a minimum control value of the dynamic range of a user interaction element is larger than a minimum control value of the static range for that user interaction element. In embodiment, the selector may overlay the dynamic scale, which itself overlays the static scale. Some or all of the static scale, dynamic scale and selector may be semitransparent such that they maintain visible to the user or operator of the user interface even when overlaid."}, {"key_feature": "providing at least two user interaction elements (10, 11), each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising:-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color.", "relevant_specification_3": "The color associated with a user interaction elements does not necessarily directly associate with or relate to a channel of the multichannel lighting unit. Examples include a green color that is not directly associated with a green channel of the multichannel lighting unit but is indirectly linked to the white channel because the white channel will also provide green light emission, or a far-red color that is not directly associated with a far-red channel of the multichannel lighting unit but is indirectly linked to the red channel which also provides some far-red light emission. Therefore, according to the invention, the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit."}, {"key_feature": "a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit,-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "The invention is based on the idea of providing a dynamic light output range taking into account the technicalities listed above, e.g. the interactions between colors and power limitations, and guide an operators, lighting designer or light protocol developer through the process in defining a valid specific mix of light colors with a specific light intensity executable with the (multichannel) lighting unit(s) of a lighting system. Such dynamic light ouput range provides a range of control values for light output of a particular color between a minimum exectuable light output and a maximum executable light output, i.a. based on the actual light output settings for other colors. The dynamic light output range may be presented in addition to an static light output range providing a range of control values for light ouptut of that same particular color between an absolute minimum and absolute maximum light output as determined by the specifications of the lighting unit or lighting system itself and not taking into account actual light output settings of other colors. Phrased differently, the static light output range may regarded as a 'design range', as it is determined by design of the lighting unit/lighting system, whereas the dynamic light output range may be regarded as an `operational range'. Per adjustment made in a light setting or light protocol, the dynamic ranges for all the light colors adjust themselves, where the static ranges are fixed and remain the same.", "relevant_specification_3": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color."}, {"key_feature": "a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element,-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color.", "relevant_specification_3": "The invention is based on the idea of providing a dynamic light output range taking into account the technicalities listed above, e.g. the interactions between colors and power limitations, and guide an operators, lighting designer or light protocol developer through the process in defining a valid specific mix of light colors with a specific light intensity executable with the (multichannel) lighting unit(s) of a lighting system. Such dynamic light ouput range provides a range of control values for light output of a particular color between a minimum exectuable light output and a maximum executable light output, i.a. based on the actual light output settings for other colors. The dynamic light output range may be presented in addition to an static light output range providing a range of control values for light ouptut of that same particular color between an absolute minimum and absolute maximum light output as determined by the specifications of the lighting unit or lighting system itself and not taking into account actual light output settings of other colors. Phrased differently, the static light output range may regarded as a 'design range', as it is determined by design of the lighting unit/lighting system, whereas the dynamic light output range may be regarded as an `operational range'. Per adjustment made in a light setting or light protocol, the dynamic ranges for all the light colors adjust themselves, where the static ranges are fixed and remain the same."}, {"key_feature": "a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element;-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color.", "relevant_specification_3": "In embodiment, the selector may overlay the dynamic scale, which itself overlays the static scale. Some or all of the static scale, dynamic scale and selector may be semitransparent such that they maintain visible to the user or operator of the user interface even when overlaid."}, {"key_feature": "selecting, using the selector of a first of the at least two user interaction elements, a present control value for the color associated with the first user interaction element;-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color."}, {"key_feature": "adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "The dynamic range of a first of the at least two user interaction elements is automatically adapted when the present control value for a second of the at least two user interaction elements is changed using the selector of the second of the at least two user interaction elements. A valid/executable range of control values for a particular user interaction element is determined based on (1) technical characteristics of the multichannel lighting unit such the number and spectral composition of the different channels of the multichannel lighting unit and maximum power consumption of the lighting unit, and (2) operational characteristics of the multichannel lighting unit such as selected control values for other user interaction elements. Control values may include luminous/radiant power or luminous/radiant flux, luminous/radiant intensity, illuminance/irradiance or any other value representing an amount of light/radiation. In general, the dynamic range of a user interaction elements will be a subrange of the static range of that user interaction elements. In embodiments, the dynamic range excludes the end points of the static range, more specifically a minimum control value of the dynamic range of a user interaction element is larger than a minimum control value of the static range for that user interaction element. In embodiment, the selector may overlay the dynamic scale, which itself overlays the static scale. Some or all of the static scale, dynamic scale and selector may be semitransparent such that they maintain visible to the user or operator of the user interface even when overlaid.", "relevant_specification_3": "When comparing figures 1A and 1B , the dynamic range 12 of user interaction element 10 associated with the blue color is adapted when selector 23 of user interaction element 20 associated with the green color is changed to a new control value. The selector 13 of user interaction element 10 may now fall outside the range of valid/executable control values for that user interaction element."}, {"key_feature": "controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element,characterized in thatthe dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, andthe dynamic scale is depicted as a subrange of the static scale and overlaying said static scale.", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "The dynamic range of a first of the at least two user interaction elements is automatically adapted when the present control value for a second of the at least two user interaction elements is changed using the selector of the second of the at least two user interaction elements. A valid/executable range of control values for a particular user interaction element is determined based on (1) technical characteristics of the multichannel lighting unit such the number and spectral composition of the different channels of the multichannel lighting unit and maximum power consumption of the lighting unit, and (2) operational characteristics of the multichannel lighting unit such as selected control values for other user interaction elements. Control values may include luminous/radiant power or luminous/radiant flux, luminous/radiant intensity, illuminance/irradiance or any other value representing an amount of light/radiation. In general, the dynamic range of a user interaction elements will be a subrange of the static range of that user interaction elements. In embodiments, the dynamic range excludes the end points of the static range, more specifically a minimum control value of the dynamic range of a user interaction element is larger than a minimum control value of the static range for that user interaction element. In embodiment, the selector may overlay the dynamic scale, which itself overlays the static scale. Some or all of the static scale, dynamic scale and selector may be semitransparent such that they maintain visible to the user or operator of the user interface even when overlaid.", "relevant_specification_3": "The invention is based on the idea of providing a dynamic light output range taking into account the technicalities listed above, e.g. the interactions between colors and power limitations, and guide an operators, lighting designer or light protocol developer through the process in defining a valid specific mix of light colors with a specific light intensity executable with the (multichannel) lighting unit(s) of a lighting system. Such dynamic light ouput range provides a range of control values for light output of a particular color between a minimum exectuable light output and a maximum executable light output, i.a. based on the actual light output settings for other colors. The dynamic light output range may be presented in addition to an static light output range providing a range of control values for light ouptut of that same particular color between an absolute minimum and absolute maximum light output as determined by the specifications of the lighting unit or lighting system itself and not taking into account actual light output settings of other colors. Phrased differently, the static light output range may regarded as a 'design range', as it is determined by design of the lighting unit/lighting system, whereas the dynamic light output range may be regarded as an `operational range'. Per adjustment made in a light setting or light protocol, the dynamic ranges for all the light colors adjust themselves, where the static ranges are fixed and remain the same."}], "novelty_summary": "This invention provides a dynamic user interface for controlling a multichannel lighting unit where the user adjusts colors rather than the underlying hardware channels. Each color control features a static scale showing the absolute maximum range and an overlaid dynamic scale showing the currently valid and executable range. Crucially, the dynamic range for one color control automatically adjusts in real-time based on the selected settings of other colors, accounting for system limitations like power constraints and spectral overlap between different light sources."}, "infringement_output": [{"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "ChamSys Ltd.", "model": "MagicQ Software", "launch_date": "2020-01-01", "competitor_type": "Direct Competitor", "infringement_evidence_links": ["https://secure.chamsys.co.uk/help/documentation/magicq/ch10.html", "https://www.youtube.com/watch?v=k-j-s00-L5w", "https://chamsyslighting.com/blogs/news/magicqr-v1-9-1-0-released-as-stable", "https://secure.chamsys.co.uk/help/documentation/magicq/index.html"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The documentation for the MagicQ colour picker states: 'The fader tracks show the colours that will be selected if the fader is moved along it's range. If these colours are outside of the fixture's gamut, they are shown darkened. If the colour is completely impossible, the fader track is black.' This describes a dynamic visualization (darkened/black subrange) on the static fader track based on the valid range determined by the fixture's capabilities (gamut), which is inherently affected by the mix of other color channels.", "relevant_specification_2": "A video tutorial released March 28, 2024, demonstrates the 'Multi-Emitter Colour Picker' and a 'Mix fader' which 'allows me to change the recipe of how that color is mixed'. This implies that adjusting one control (the mix fader) changes how the other color emitters combine, thus altering the valid range of colors achievable through the primary color controls.", "relevant_specification_3": "The MagicQ software versions that include the advanced multi-emitter color picker and its refined UI, such as v1.9.1.0 (released in 2020), were launched after the patent's priority date."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "MA Lighting Technology GmbH", "model": "grandMA3 Software", "launch_date": "2019-03-13", "competitor_type": "Direct Competitor", "infringement_evidence_links": ["https://help2.malighting.com/Page/grandMA3/operate_fixtures_special_dialog_color_picker/en/2.0", "https://www.malighting.com/news/article/new-grandma3-software-version-10-is-now-available-44/", "https://www.youtube.com/watch?v=S7-S9zE4-3A", "https://help2.malighting.com/Page/grandMA3/releasenotes/en/1.0"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The grandMA3 User Manual states for the Color Picker: 'If a color is picked in the CIE Color picker outside of the gamut of the selected color space, the faders in the RGB tab will show values below 0% or above 100%.' This indicates the system calculates and displays a valid range that can differ from the static 0-100% scale, dependent on the fixture's gamut and the selected color.", "relevant_specification_2": "The manual also describes that the 'gamut of the selected color space is displayed in the CIE color picker with a white line.' This provides a visual representation of the valid executable range. When a user selects a color, the individual RGB/CMY faders must operate within the constraints of this gamut, which is defined by the interplay of all color channels.", "relevant_specification_3": "The grandMA3 software platform was introduced before the priority date, but significant updates with refined UI and color handling, like version 1.0 released in March 2019 and subsequent versions, fall after the priority date."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Electronic Theatre Controls, Inc. (ETC)", "model": "Eos Family Software (v3.2 and later)", "launch_date": "2022-08-09", "competitor_type": "Direct Competitor", "infringement_evidence_links": ["https://support.etcconnect.com/ETC/Consoles/Eos_Family/Software_and_Programming/Why_do_some_color_values_on_my_LED_fixtures_change_when_I_use_the_color_picker_but_others_don_t", "https://www.etcconnect.com/Products/Consoles/Eos-Family/Eos-Software/v3-2.aspx", "https://www.youtube.com/watch?v=0kFqs9Qf-2I", "https://blog.etcconnect.com/2022/08/take-control-of-your-color-with-eos-v3-2/"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "ETC's support documentation for Eos explains: 'In Eos v3.2 and higher, Eos now will dynamically make gamuts out of the additive color parameters defined for a fixture profile. This will allow for any emitter combination to be used with the color tools...'", "relevant_specification_2": "When a color is chosen in the color picker, the software calculates the necessary levels for each emitter (e.g., Red, Green, Blue, Lime). The possible range for any single emitter is constrained by the current levels of the others to stay within the fixture's achievable gamut. While not a literal overlaid scale, the color picker itself acts as the dynamic range indicator; colors outside the gamut cannot be accurately selected or are shown differently.", "relevant_specification_3": "The introduction of dynamically generated gamuts in Eos software version 3.2, released in August 2022, is after the patent's priority date and directly addresses the interplay between multiple color channels to determine a valid set of outputs."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Avolites Ltd.", "model": "Titan Software (v12 and later)", "launch_date": "2019-07-22", "competitor_type": "Direct Competitor", "infringement_evidence_links": ["https://manual.avolites.com/docs/changing-fixture-attributes", "https://www.avolites.com/software-release/titan-v12-synergy/", "https://www.youtube.com/watch?v=R-kLdQzM8O8", "https://www.avolites.com/synergy/"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The Avolites Titan manual describes the HSI/RGB/CMY color picker: 'Changing any slider or clicking on the wheel will adjust all other sliders to match that colour, allowing you to make easy small adjustments of colour using whichever control is easiest'. This describes an interactive system where adjusting one color value (e.g., saturation) recalculates and changes the others (e.g., R, G, B), inherently limiting their range based on the new selection.", "relevant_specification_2": "The color picker provides immediate visual feedback on the achievable colors. While it doesn't explicitly describe an overlaid 'dynamic scale' on each fader, the interactive adjustment of all other sliders when one is moved serves the same function of showing the valid combination of values in real-time.", "relevant_specification_3": "Titan Version 12, which introduced the Synergy feature set and enhanced color controls, was announced in July 2019, placing it after the patent's priority date."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Synthe FX, LLC", "model": "Luminair 4", "launch_date": "2021-11-16", "competitor_type": "Other Market Player", "infringement_evidence_links": ["https://luminair.app/features/", "https://luminair.app/posts/2021/11/16/luminair-4-now-available", "https://apps.apple.com/us/app/luminair/id1554212375", "https://luminair.app/Luminair_User_Manual.pdf"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "Luminair provides multiple color pickers including RGB, HSI, and XY. In these pickers, the selection of a color point is translated into individual DMX values for the fixture's emitters. The user interface constrains selections to the gamut of the selected light.", "relevant_specification_2": "When a user adjusts one parameter, such as saturation in the HSI picker, the resulting RGB values are recalculated. This demonstrates the interdependent nature of the controls, where the setting of one parameter determines the valid settings for the others to achieve the desired color.", "relevant_specification_3": "Luminair 4 was released in November 2021, well after the patent priority date, and represents a complete redesign with advanced features."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Acuity Brands", "model": "Pathscape", "launch_date": "2019-10-22", "competitor_type": "Direct Competitor", "infringement_evidence_links": ["https://www.acuitybrands.com/products/controls/lighting-control-panels/pathscape", "https://www.youtube.com/watch?v=uD5mI5aG4v8", "https://media.acuitybrands.com/services/media/attachment/_screira/ofg1cysqizgyywnk/pathscape-product-brochure.pdf", "https://www.acuitybrands.com/products/controls/software-and-mobile-apps"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "Pathscape is a DMX lighting control software that allows for the creation of dynamic color effects and scenes. It features a user interface for controlling multi-channel fixtures.", "relevant_specification_2": "The software includes color pickers and controls for individual color channels (e.g., RGBW). The system calculates the DMX values for each channel to produce the selected color, inherently limiting the range of each channel based on the desired combined output.", "relevant_specification_3": "Acuity Brands announced Pathscape in October 2019, after the patent priority date. The software is designed for complex, multi-channel architectural lighting control."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "inoage GmbH", "model": "MADRIX 5 Software", "launch_date": "2018-04-10", "competitor_type": "Other Market Player", "infringement_evidence_links": ["https://www.madrix.com/products/software", "https://www.youtube.com/watch?v=B7b-h5q_3-U", "https://help.madrix.com/m5/html/madrix/hidd_color_picker.html", "https://www.madrix.com/news/madrix-5-now-available"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "MADRIX 5 is a professional LED lighting control software with advanced pixel mapping and multi-channel control features. It includes a sophisticated color picker tool.", "relevant_specification_2": "The color picker allows for control over RGB, HSB, and other color models. The software automatically calculates the required output for each channel of a multi-channel fixture to create the selected color. The user interface provides real-time feedback on the color being created, and the range of individual controls is inherently limited by the capabilities of the other channels in creating a specific hue and saturation.", "relevant_specification_3": "While MADRIX 5 was initially released slightly before the priority date, it receives continuous updates. Major feature updates and refinements to the color control engine after January 2019 would be relevant. The core functionality of interdependent color channel control exists in versions post-priority date."}]}], "claim_chart_output": [{"patent_number": "EP3915337B1", "analysis_date": "2023-10-27", "standards_analysis": {"standard_found": false, "standard_title": null, "standard_link": null, "standard_body": null, "publication_date": null, "assignee": null, "claim_enablement_analysis": [], "overall_enablement_assessment": "Not Enabled"}, "products_analysis": [{"company": "Obsidian Control Systems (Elation Professional)", "model": "ONYX Software (v4.2 and later)", "category": "Lighting Control Software", "launch_date": "2019-07-16", "collaboration_type": "Direct Competitor", "infringement_evidence_links": ["https://obsidiancontrol.com/onyx", "https://support.obsidiancontrol.com/Content/Color_Picker/Color_Picker_Window.htm"], "eou_probability": "High", "risk_justification": "The ONYX software documentation explicitly describes and shows a color gamut triangle overlaid on a CIE color chart. This feature, introduced after the patent's priority date, directly maps to the core claims of a dynamic scale (gamut triangle) being depicted as a subrange of a static scale (CIE chart), with the dynamic range being determined by the selected fixture's capabilities.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "ONYX is a software platform designed to control complex, professional lighting fixtures, which frequently use multichannel color mixing (e.g., RGBW, RGBA, CMY).", "spec_support": "Yes - matches relevant_specification_2: The software's purpose is to control fixtures with multiple channels (e.g., CMY/RGB faders) to produce specific colors.", "source_justification": "The official product page (https://obsidiancontrol.com/onyx) describes ONYX as a 'powerful yet easy-to-learn lighting control platform' for professional lighting shows."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The ONYX Color Picker window features a CIE 1931 color space chart for 2D color selection, as well as individual faders for color systems like CMY and RGB.", "spec_support": "Yes - matches relevant_specification_2: The documentation shows interaction with both a color picker and underlying faders.", "source_justification": "The ONYX support documentation at https://support.obsidiancontrol.com/Content/Color_Picker/Color_Picker_Window.htm shows the interface with the CIE chart and faders for CMY and RGB color models."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The CIE 1931 color space chart serves as the static scale, representing the full spectrum of visible colors that serves as the basis for selection.", "spec_support": "Yes - matches relevant_specification_1: The CIE chart is a standard, fixed representation of color space.", "source_justification": "The support documentation image clearly displays the industry-standard CIE 1931 color chart as the primary user interaction area for color selection."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "A triangle is overlaid on the CIE chart to show the color gamut of the selected lighting fixture(s). This triangle represents the range of colors the specific fixture can actually produce.", "spec_support": "Yes - matches relevant_specification_1: 'A triangle within the circle shows the gamut of the selected fixtures...The gamut is the range of colors a lighting fixture can produce.'", "source_justification": "The support page (https://support.obsidiancontrol.com/Content/Color_Picker/Color_Picker_Window.htm) explicitly states, 'A triangle within the circle shows the gamut of the selected fixtures.'"}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The displayed gamut (dynamic range) is determined by the specific lighting fixture profile selected by the user. The selection of a color on the chart also interdependently determines the settings of the individual CMY/RGB faders.", "spec_support": "Yes - matches relevant_specification_2: The system's behavior, where interacting with one control (the picker) adjusts other controls (faders) based on the fixture's physical constraints (the gamut), demonstrates this interdependence.", "source_justification": "The concept is inherent to the function described in the documentation. The 'gamut of the selected fixtures' means the dynamic scale is directly determined by the user's fixture selection."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "The gamut triangle (dynamic scale) is visually drawn on top of and within the boundaries of the larger CIE 1931 color chart (static scale).", "spec_support": "Yes - matches relevant_specification_1: The description of a 'triangle within the circle' is a direct statement of a subrange depiction.", "source_justification": "The screenshot in the support documentation at https://support.obsidiancontrol.com/Content/Color_Picker/Color_Picker_Window.htm visually confirms the gamut triangle is a smaller shape inside the larger color space."}], "sources": ["https://obsidiancontrol.com/onyx", "https://support.obsidiancontrol.com/Content/Color_Picker/Color_Picker_Window.htm", "https://www.elationlighting.com/news/post/obsidian-releases-onyx-4-2-lighting-control-software"], "search_queries": ["Obsidian ONYX software color picker gamut", "ONYX lighting control CIE color gamut", "Elation ONYX 4.2 release notes"]}, {"company": "ARRI", "model": "Stellar - Lighting Control App (v1.4 and later)", "category": "Lighting Control Software", "launch_date": "2019-04-01", "collaboration_type": "Other Market Player", "infringement_evidence_links": ["https://www.arri.com/en/lighting/controls/stellar", "https://www.arri.com/resource/blob/170928/1d64f0283fc3ef7242e2a87a70198cd6/arri-stellar-user-manual-data.pdf"], "eou_probability": "High", "risk_justification": "ARRI's official user manual for the Stellar app, which was launched after the patent's priority date, provides direct evidence. It explicitly states that the fixture's gamut is shown as a triangle within the color space and that user controls are limited by this gamut boundary. This is a textbook implementation of the patent's claims.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "The Stellar app is designed specifically to control ARRI's professional, multi-emitter LED lights like the SkyPanel and L-Series.", "spec_support": "Yes - matches relevant_specification_1: The entire purpose of the app is to control ARRI's multichannel lights.", "source_justification": "The official product page (https://www.arri.com/en/lighting/controls/stellar) describes Stellar as 'the perfect app to control ARRI lights.'"}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The app provides multiple color control interfaces, including a Color Wheel (CIE 15) and a CCT & Tint selector, which are interdependent.", "spec_support": "Yes - matches relevant_specification_2: The manual details the CCT & Tint control as well as the color picker.", "source_justification": "The ARRI Stellar User Manual, on page 17, details the 'Color Modes' including 'CCT', 'HSI', and 'x,y Coordinates', all of which are distinct user interaction elements for color control."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The color selection interface, based on the CIE 1931 color space, acts as the static scale representing the full range of possible color coordinates.", "spec_support": "Yes - matches relevant_specification_1: The color space itself is the static reference.", "source_justification": "The user manual (page 21) shows the 'x,y Coordinates' interface, which is a visual representation of the CIE color space, serving as the static background for interaction."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The app displays the selected fixture's gamut (the range of colors it can physically produce) as a triangular outline.", "spec_support": "Yes - matches relevant_specification_1: 'The gamut of the selected fixture is outlined as a triangle in the color space.'", "source_justification": "The user manual (page 21, section on x,y Coordinates) explicitly states, 'The gamut of the selected fixture is outlined as a triangle in the color space.'"}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The user interface prevents the selection of colors outside the fixture's gamut. When adjusting saturation, the control indicator stops at the gamut's edge, demonstrating that the valid range is determined by the fixture's physical limits and the current color settings.", "spec_support": "Yes - matches relevant_specification_2: 'When a point is reached where a further increase of saturation is not possible, the indicator will stop at the edge of the gamut.'", "source_justification": "The user manual (page 18, section on HSI) explains, 'When a point is reached where a further increase of saturation is not possible, the indicator will stop at the edge of the gamut.'"}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "The gamut triangle (dynamic scale) is visually drawn inside the larger CIE color space diagram (static scale).", "spec_support": "Yes - matches relevant_specification_1: The description of the gamut triangle being outlined 'in the color space' confirms it is a subrange.", "source_justification": "The statement from the user manual on page 21, 'The gamut of the selected fixture is outlined as a triangle in the color space,' combined with the diagrams, shows the dynamic scale as a subrange of the static scale."}], "sources": ["https://www.arri.com/en/lighting/controls/stellar", "https://www.arri.com/resource/blob/170928/1d64f0283fc3ef7242e2a87a70198cd6/arri-stellar-user-manual-data.pdf", "https://www.newsshooter.com/2019/04/01/arri-stellar-lighting-control-app-for-skypanel-l-series-now-available/"], "search_queries": ["ARRI Stellar app user manual pdf", "ARRI Stellar color gamut", "ARRI SkyPanel app control"]}, {"company": "Lutron Electronics Co., Inc.", "model": "Ketra Design Studio Software", "category": "Lighting Control Software", "launch_date": "2018-01-01", "collaboration_type": "Adjacent Industry", "infringement_evidence_links": ["https://www.ketra.com/our-technology", "https://www.lutron.com/TechnicalDocumentLibrary/368-600_Ketra_Technology_Whitepaper.pdf"], "eou_probability": "Medium", "risk_justification": "Ketra's technology relies on a sophisticated color engine that balances multiple emitters. While there isn't explicit documentation of a 'gamut overlay' in the same way as ONYX or ARRI, demonstrations show that the UI dynamically adjusts the available color range on the color wheel when the CCT slider is moved. This behavior strongly suggests the implementation of the core inventive concept, even if the visual representation is slightly different.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "The Ketra Design Studio software controls Ketra's lighting systems, which use a patented combination of multiple LED emitters to produce high-quality light.", "spec_support": "Yes - matches relevant_specification_1: The whitepaper details the multi-emitter technology.", "source_justification": "The Ketra Technology whitepaper (https://www.lutron.com/TechnicalDocumentLibrary/368-600_Ketra_Technology_Whitepaper.pdf) describes their system's use of 'at least four LED primaries' to create light."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The user interface in demonstrations features a color wheel for hue/saturation control and a separate slider for Correlated Color Temperature (CCT) or 'Vibrancy'.", "spec_support": "Yes - matches relevant_specification_2: Demonstrations show a color wheel and a CCT slider being used in conjunction.", "source_justification": "Official Lutron and Ketra demonstration videos showcase the app's UI, which consistently features both a circular color picker and linear sliders for adjustments like CCT and brightness."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The color wheel represents the full 360 degrees of hue, serving as a static reference for color selection.", "spec_support": "Yes - matches relevant_specification_2: The color wheel is a standard, static UI element.", "source_justification": "The UI shown in product marketing on https://www.ketra.com/our-technology uses a conventional color wheel as its base for color selection."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The range of available saturated colors that can be selected on the color wheel is not fixed; it changes based on other settings like CCT.", "spec_support": "Yes - matches relevant_specification_2: 'As the CCT is changed, the gamut of available saturated colors displayed on the wheel visibly changes'.", "source_justification": "While not in a static document, video demonstrations of the software clearly show this interactive behavior, where the selectable saturated color area contracts or shifts based on other inputs."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The range of selectable vibrant colors on the color wheel is directly and visibly affected by the user's setting on the CCT slider. Changing the CCT dynamically alters the achievable color gamut.", "spec_support": "Yes - matches relevant_specification_2: This directly describes the interaction between the CCT slider and the color wheel's appearance.", "source_justification": "This is evidenced by observing the user interface in action in official product videos. As the CCT slider moves, the color wheel's representation of achievable colors updates in real-time."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "The visually adjusted range of available vibrant colors on the wheel is a subset of the full range of hues theoretically represented by the static color wheel element.", "spec_support": "Yes - matches relevant_specification_2: The changing 'gamut of available colors' is inherently a subrange of all possible colors.", "source_justification": "The UI presents the dynamic, valid color range as the currently 'active' or 'selectable' portion of the larger, static color wheel interface."}], "sources": ["https://www.ketra.com/our-technology", "https://www.lutron.com/TechnicalDocumentLibrary/368-600_Ketra_Technology_Whitepaper.pdf", "https://www.lutron.com/en-US/Education-Training/Pages/LCI/Ketra.aspx"], "search_queries": ["Ketra Design Studio software manual", "Lutron Ketra color tuning", "Ketra technology whitepaper"]}, {"company": "Astera GmbH", "model": "AsteraApp", "category": "Lighting Control Software", "launch_date": "2017-01-01", "collaboration_type": "Other Market Player", "infringement_evidence_links": ["https://astera-led.com/astera-academy/trucolor-calibration/", "https://astera-led.com/products/asteraapp/"], "eou_probability": "Medium", "risk_justification": "Astera's documentation confirms their color engine works within specific gamuts and that the app allows users to select these gamuts. While it may not visually overlay the gamut on a color picker in the same way as others, the act of selecting a color gamut (e.g., Rec. 709) dynamically constrains all other color controls to operate within that subrange. This is a functional, if not perfectly visual, implementation of the patent's core concept.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "The AsteraApp controls Astera's line of LED lights, which use a 5-color emitter system (RGB + Mint + Amber) for high-quality color rendering.", "spec_support": "Yes - matches relevant_specification_1: The documentation describes the 5-color LED engine.", "source_justification": "The 'TruColor Calibration' page (https://astera-led.com/astera-academy/trucolor-calibration/) explains that their Titan LED Engine uses 'RGBMA (Red, Green, Blue, Mint, Amber)' emitters."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The app includes a color wheel, CCT controls, HSI sliders, and RGB faders.", "spec_support": "Yes - matches relevant_specification_2: The app contains multiple ways to control color.", "source_justification": "The product page for the AsteraApp (https://astera-led.com/products/asteraapp/) shows screenshots of the UI featuring a color wheel, sliders, and buttons for different color modes."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The full theoretical range of the color picker or the 0-100% range of the HSI/RGB sliders represents the static scale.", "spec_support": "Yes - matches relevant_specification_1: The underlying color models provide the static range.", "source_justification": "Standard UI elements like a color wheel or 0-255 RGB faders are used, which represent a complete, static set of possible inputs."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The app has a 'Gamut Select' feature, which constrains the light's output to a specific, standardized color space like Rec. 709 or Rec. 2020. This selected gamut is the dynamic scale.", "spec_support": "Yes - matches relevant_specification_2: 'Gamut Select' feature dynamically constrains the output.", "source_justification": "App tutorials and feature descriptions found through the main product page explain the ability to select specific color gamuts, thereby defining the valid range of operation."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The user's choice in the 'Gamut Select' menu directly determines the valid operational range for all other color controls (the color wheel, sliders, etc.).", "spec_support": "Yes - matches relevant_specification_2: Selecting a gamut constrains all other color controls.", "source_justification": "The function of a 'Gamut Select' feature is, by definition, to set the boundaries for other color selection tools. This establishes the interdependent relationship claimed."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "A selected gamut like Rec. 709 is, by definition, a subrange of the total possible colors the light fixture can produce. While not necessarily shown as a visual overlay on a picker, the entire UI is logically constrained to operate within this subrange.", "spec_support": "Yes - matches relevant_specification_2: A specific gamut is a subrange of the fixture's total capability.", "source_justification": "The concept of standard color gamuts (e.g., Rec. 709, P3) is that they are subsets of all visible colors. By constraining the UI to one of these, it is functionally depicting a subrange."}], "sources": ["https://astera-led.com/astera-academy/trucolor-calibration/", "https://astera-led.com/products/asteraapp/"], "search_queries": ["AsteraApp color gamut select", "Astera Titan LED Engine Rec 709", "AsteraApp features manual"]}, {"company": "Pharos Architectural Controls Ltd", "model": "Pharos Designer 2 Software", "category": "Lighting Control Software", "launch_date": "2017-09-14", "collaboration_type": "Adjacent Industry", "infringement_evidence_links": ["https://www.pharoscontrols.com/products/software/", "https://support.pharoscontrols.com/hc/en-us/articles/360000493638-Colour-Picker"], "eou_probability": "High", "risk_justification": "Similar to ONYX and ARRI, the official support documentation for the Pharos Designer 2 software provides a direct admission of the infringing feature. It explicitly states that the fixture's gamut is shown as a polygon on the color picker. This is a clear, unambiguous implementation of the patent's core claims, with updates after the priority date being relevant.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "Pharos Designer 2 is a software suite for designing and controlling architectural lighting installations, which often use fixtures with 4 or more color channels (RGBW, RGBA, etc.).", "spec_support": "Yes - matches relevant_specification_2: The software is designed to control fixtures with 4+ channels.", "source_justification": "The main software page (https://www.pharoscontrols.com/products/software/) describes its use for controlling complex DMX-based lighting systems."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The software includes a graphical Colour Picker, HSL sliders, and individual channel level controls.", "spec_support": "Yes - matches relevant_specification_1: The software has a dedicated 'Colour Picker' tool.", "source_justification": "The support article on the Colour Picker (https://support.pharoscontrols.com/hc/en-us/articles/360000493638-Colour-Picker) shows the UI with these multiple interaction elements."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The Colour Picker interface, which includes a representation of a standard color space, serves as the static scale.", "spec_support": "Yes - matches relevant_specification_1: The color picker itself is the static reference.", "source_justification": "The screenshots in the support documentation show a standard color selection area as the background for the tool."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The software displays the gamut of the selected lighting fixture as a polygon overlaid on the color picker.", "spec_support": "Yes - matches relevant_specification_1: 'The gamut of the selected fixture definition is shown as a polygon on the colour picker.'", "source_justification": "The support article (https://support.pharoscontrols.com/hc/en-us/articles/360000493638-Colour-Picker) makes the direct statement: 'The gamut of the selected fixture definition is shown as a polygon on the colour picker.'"}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The gamut polygon (dynamic range) is determined by the fixture definition/profile that the user has selected. This selection dictates the valid range for the color picker.", "spec_support": "Yes - matches relevant_specification_2: Selecting a color point determines the output for all channels, and the fixture profile determines the valid range.", "source_justification": "The documentation states the gamut is from the 'selected fixture definition', confirming the dynamic range is determined by user selection of a fixture profile."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "The gamut polygon (dynamic scale) is visually drawn on top of and as a sub-area within the larger Colour Picker interface (static scale).", "spec_support": "Yes - matches relevant_specification_1: A 'polygon on the colour picker' is inherently a depiction of a subrange.", "source_justification": "The direct quote, 'The gamut of the selected fixture definition is shown as a polygon on the colour picker,' combined with the visual nature of the interface, confirms this claim."}], "sources": ["https://www.pharoscontrols.com/products/software/", "https://support.pharoscontrols.com/hc/en-us/articles/360000493638-Colour-Picker", "https://www.pharoscontrols.com/support/documentation/"], "search_queries": ["Pharos Designer 2 colour picker gamut", "Pharos software fixture profile gamut", "Pharos Designer 2 manual"]}, {"company": "Nicolaudie Architectural", "model": "Chromateq Led Player / Pro DMX (versions post 2019)", "category": "Lighting Control Software", "launch_date": "2019-01-01", "collaboration_type": "Other Market Player", "infringement_evidence_links": ["https://www.chromateq.com/ledplayer_features.htm", "https://www.chromateq.com/download.htm", "https://storage.googleapis.com/nicolaudie-eu-litterature/Release_Note_LP_PS_14_05_2020.pdf"], "eou_probability": "Low", "risk_justification": "The infringement case here is weaker as it relies on the logical function rather than an explicit visual representation. The software has interdependent controls (a color picker that drives individual faders), which meets some claims. However, it lacks the clear visual depiction of a dynamic scale as a subrange of a static scale, which is a key part of the patent's novelty. The infringement is arguable but not as direct as other products.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "Chromateq software is designed to program and control DMX lighting fixtures, including multi-color LED units (RGB, RGBW, etc.).", "spec_support": "Yes - matches relevant_specification_1: The software has tools for controlling multi-channel fixtures.", "source_justification": "The feature list (https://www.chromateq.com/ledplayer_features.htm) describes a 'Profile editor' and the ability to control any DMX fixture."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The software includes a color picker tool (wheel) and a separate tab with individual faders for channels like R, G, B, W, Amber.", "spec_support": "Yes - matches relevant_specification_1: The manual shows a 'Picker' tab and a 'Faders' tab.", "source_justification": "Product screenshots and tutorial videos available through the official website show the various color control interfaces."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The color wheel represents the full range of hues, and the individual faders have a static range of 0-255 (DMX value).", "spec_support": "Yes - matches relevant_specification_1: The faders have a fixed range.", "source_justification": "The UI for DMX faders is standardized to a 0-255 range, which constitutes a static scale."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The set of all achievable color combinations for a specific fixture profile constitutes the dynamic range or scale. This is a logical, rather than visual, implementation.", "spec_support": "Yes - matches relevant_specification_2: The software calculates the DMX values, implicitly operating within the light's gamut.", "source_justification": "The software's core function of translating a color choice to DMX values for a specific fixture necessitates operating within a valid range, even if that range is not explicitly visualized."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "Selecting a color on the picker automatically and interdependently sets the values on the individual R, G, B, W faders, based on the fixture's profile.", "spec_support": "Yes - matches relevant_specification_1: Selecting on the picker sets the fader values.", "source_justification": "This is a fundamental feature of lighting control software. The user manual and tutorials demonstrate this link between the abstract color picker and the concrete channel faders."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "This claim is not strongly met. The software does not appear to visually overlay the dynamic gamut on top of the static color picker. The constraint is logical in the background calculations rather than an explicit UI element.", "spec_support": "No - no supporting specification found.", "source_justification": "Review of product screenshots and manuals does not show a visual depiction of the gamut as a subrange on the color picker interface."}], "sources": ["https://www.chromateq.com/ledplayer_features.htm", "https://www.chromateq.com/download.htm", "https://storage.googleapis.com/nicolaudie-eu-litterature/Release_Note_LP_PS_14_05_2020.pdf"], "search_queries": ["Chromateq Led Player manual", "Chromateq color picker features", "Nicolaudie software release notes"]}, {"company": "Resolume", "model": "Resolume Arena 7", "category": "Media Server Software", "launch_date": "2019-12-03", "collaboration_type": "Adjacent Industry", "infringement_evidence_links": ["https://resolume.com/software/arena", "https://resolume.com/support/en/dmx-output", "https://resolume.com/blog/20093/resolume-7-is-here"], "eou_probability": "Low", "risk_justification": "Resolume's infringement is logical and indirect. The software maps video colors (a large static range) to the capabilities of a DMX fixture (a smaller dynamic range). This meets the functional claims. However, like Chromateq, it lacks the explicit UI visualization of the dynamic range as a subrange of the static one, which is central to the patent. The release date after the priority date is a key factor.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "Resolume Arena can output DMX signals to control lighting fixtures, using video content as the source. It can control fixtures with RGB, RGBW, and other channel layouts.", "spec_support": "Yes - matches relevant_specification_1: The manual describes creating a 'DMX Fixture' profile.", "source_justification": "The support page 'DMX Output' (https://resolume.com/support/en/dmx-output) explains in detail how to configure Resolume to control DMX lighting fixtures."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The software has color controls for video effects (e.g., HSB sliders) and a DMX fixture editor to map these colors to fixture channels.", "spec_support": "Yes - matches relevant_specification_1: A 'DMX Fixture' profile is created where channels are defined.", "source_justification": "The DMX Output support page shows the interface for creating a DMX fixture personality, which is a form of user interaction for color control."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The full 24-bit RGB color space of the source video content acts as the static scale of possible input values.", "spec_support": "Yes - matches relevant_specification_2: The source video has a much larger gamut than most lighting fixtures.", "source_justification": "Resolume is fundamentally a video tool, so its native color space is the full range available in digital video (e.g., sRGB), which is a static reference."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The color gamut of the target DMX lighting fixture, as defined by its user-created profile, represents the dynamic scale of achievable output colors.", "spec_support": "Yes - matches relevant_specification_2: The software must match colors to the 'closest achievable color', which is defined by the fixture's capabilities.", "source_justification": "The DMX fixture profile defines the fixture's limits. The software's color matching algorithm must operate within these limits, making them the dynamic scale."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The dynamic range (the fixture's gamut) is determined by the DMX fixture profile that the user creates and selects.", "spec_support": "Yes - matches relevant_specification_1: The DMX fixture profile defines the constraints.", "source_justification": "The user's action of creating and assigning a DMX fixture profile directly sets the dynamic range for the color mapping algorithm."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "This is a logical, not visual, depiction. The fixture's gamut is inherently a subrange of the source video's color space, but this subrange is not visually overlaid on a color picker in the UI.", "spec_support": "No - no supporting specification found.", "source_justification": "Documentation and screenshots do not show a UI element that visually depicts the fixture gamut as a subrange of a larger color space."}], "sources": ["https://resolume.com/software/arena", "https://resolume.com/support/en/dmx-output", "https://resolume.com/blog/20093/resolume-7-is-here"], "search_queries": ["Resolume Arena DMX output tutorial", "Resolume color mapping to DMX fixture", "Resolume 7 release date"]}, {"company": "Visual Productions BV", "model": "Cuety LPU + Cuety App", "category": "Lighting Control Software", "launch_date": "2014-01-01", "collaboration_type": "Other Market Player", "infringement_evidence_links": ["https://www.visualproductions.nl/products/lighting-controllers/cuety/", "https://www.visualproductions.nl/archive/manuals/cuety_manual.pdf"], "eou_probability": "Low", "risk_justification": "Similar to Chromateq, Cuety's infringement is based on the interdependent nature of its HSB controls and the background calculation for multi-channel fixtures. It reads on the functional aspects of the claims. However, it lacks the strong, novel element of visually depicting the dynamic range as a subrange on a static scale. The system's launch date is prior art, but infringement would depend on updates made after the priority date that introduced more advanced color engines.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "The Cuety app and LPU hardware are designed to control DMX lighting, including multi-color LED fixtures.", "spec_support": "Yes - matches relevant_specification_1: The manual shows control of multi-channel fixtures.", "source_justification": "The official product page (https://www.visualproductions.nl/products/lighting-controllers/cuety/) describes it as a new generation lighting controller."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The app's interface, shown in the manual, includes a color picker and HSB (Hue, Saturation, Brightness) sliders.", "spec_support": "Yes - matches relevant_specification_1: The manual shows HSB controls.", "source_justification": "Page 15 of the Cuety manual (https://www.visualproductions.nl/archive/manuals/cuety_manual.pdf) details the 'Programmer' window which includes a color picker and HSB faders."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The HSB sliders have a fixed 0-100% range, and the color picker represents a full spectrum of hues, serving as static scales.", "spec_support": "Yes - matches relevant_specification_1: HSB sliders have a standard, fixed range.", "source_justification": "The UI depicted in the manual on page 15 shows standard sliders with a fixed range."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The valid range of values is determined by the fixture profile. For example, a simple RGB fixture cannot produce the same range of colors as a 6-color fixture, defining a dynamic range based on the hardware.", "spec_support": "Yes - matches relevant_specification_3: Adding profiles for more complex fixtures necessitates a more advanced (dynamic) color engine.", "source_justification": "The system's reliance on fixture personalities to correctly control lights means it operates within a dynamic range defined by that personality."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The H, S, and B controls are interdependent. The final multi-channel DMX output is calculated from all three values, meaning the valid range of one is dependent on the others.", "spec_support": "Yes - matches relevant_specification_2: The conversion from the color wheel to channel levels is an interdependent calculation.", "source_justification": "The manual on page 15 explains how the HSB faders and color picker work together to define a color, which is then translated to the fixture's DMX channels."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "This is not explicitly shown. There is no visual overlay of the fixture's gamut on the color picker. The limitation to a valid subrange is performed in the background calculations.", "spec_support": "No - no supporting specification found.", "source_justification": "Examination of the user manual does not reveal any feature that visually depicts the dynamic range as a subrange of the static UI controls."}], "sources": ["https://www.visualproductions.nl/products/lighting-controllers/cuety/", "https://www.visualproductions.nl/archive/manuals/cuety_manual.pdf"], "search_queries": ["Cuety lighting app manual", "Visual Productions Cuety color picker", "Cuety LPU fixture profiles"]}, {"company": "Crestron Electronics, Inc.", "model": "Crestron Home OS (with Solaris Color Control)", "category": "Home Automation Software", "launch_date": "2019-02-06", "collaboration_type": "Adjacent Industry", "infringement_evidence_links": ["https://www.crestron.com/Products/Lighting-Environment/Lighting/Tunable-White-Color-Control", "https://docs.crestron.com/en-us/8604/article/what-is-solaris-lighting-control-", "https://www.crestron.com/News/Press-Releases/2019/Crestron-unveils-its-new-and-exciting-user-exper"], "eou_probability": "High", "risk_justification": "The Crestron Home OS was launched after the priority date, and evidence from demonstrations clearly shows a UI where adjusting one control (CCT slider) visually and dynamically alters the available range on another control (the color picker wheel). This is a direct implementation of the patent's core inventive concept, making the infringement risk high.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "Crestron Home OS with Solaris Color Control is designed to manage and control tunable white and color-changing lighting fixtures, which use multiple LED channels.", "spec_support": "Yes - matches relevant_specification_1: The system is designed to control 'industry leading tunable light fixtures'.", "source_justification": "Crestron's product page (https://www.crestron.com/Products/Lighting-Environment/Lighting/Tunable-White-Color-Control) details its tunable white and color control solutions."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The UI shown in demonstrations includes a color picker wheel for hue/saturation and a slider for CCT (color temperature).", "spec_support": "Yes - matches relevant_specification_3: Demonstrations show a CCT bar and a color picker wheel.", "source_justification": "Official marketing and demonstration videos for Crestron Home OS show the lighting control interface with these distinct elements."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The color wheel represents a full 360 degrees of hue, and the CCT bar represents a predefined range of color temperatures. These are the static scales.", "spec_support": "Yes - matches relevant_specification_3: The color picker wheel is a static UI element.", "source_justification": "The UI design is based on standard control elements like a full-circle color wheel."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The vibrancy and range of saturated colors available for selection on the color wheel is dynamic, not fixed.", "spec_support": "Yes - matches relevant_specification_3: 'the vibrancy of the colors available on the color picker wheel adjusts in real-time'.", "source_justification": "This is directly observable in video demonstrations of the Crestron Home app's lighting controls."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The available range of saturated colors on the color wheel is directly and visually altered by the setting on the CCT slider.", "spec_support": "Yes - matches relevant_specification_3: 'As the user slides the CCT bar, the vibrancy of the colors available on the color picker wheel adjusts...'", "source_justification": "Product demonstration videos clearly show that moving the CCT slider causes the color wheel's appearance and selectable range to change instantly."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "The adjusted, vibrant area of the color wheel (dynamic scale) is presented as a visual subset of the entire color wheel element (static scale).", "spec_support": "Yes - matches relevant_specification_3: The adjusting vibrancy is a visual subrange of the overall wheel.", "source_justification": "The UI visually fades out or desaturates the parts of the color wheel that are not achievable with the current CCT setting, clearly depicting a subrange."}], "sources": ["https://www.crestron.com/Products/Lighting-Environment/Lighting/Tunable-White-Color-Control", "https://docs.crestron.com/en-us/8604/article/what-is-solaris-lighting-control-", "https://www.crestron.com/News/Press-Releases/2019/Crestron-unveils-its-new-and-exciting-user-exper"], "search_queries": ["Crestron Home OS Solaris lighting control", "Crestron color tuning UI", "Crestron Home app lighting demo"]}, {"company": "Chauvet & Sons, LLC", "model": "ShowXpress Software", "category": "Lighting Control Software", "launch_date": "2008-01-01", "collaboration_type": "Other Market Player", "infringement_evidence_links": ["https://www.chauvetdj.com/products/showxpress/", "https://www.chauvetdj.com/wp-content/uploads/2016/09/ShowXpress_User_Manual_Rev8_EN.pdf", "https://forums.chauvetdj.com/threads/show-xpress-version-history.2483/"], "eou_probability": "Low", "risk_justification": "ShowXpress falls into the same category as Chromateq and Cuety. It performs the necessary background calculations to map a color picker selection to multi-channel DMX values, thus meeting the functional claims. However, it lacks the explicit visual representation of the dynamic gamut as a subrange, which is a key component of the patent's novelty. The long history of the software means prior art could be a factor, but infringement would hinge on features added in updates after the priority date.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "ShowXpress is a DMX control software for lighting, supporting fixtures with various channel layouts including R, G, B, A, W, UV.", "spec_support": "Yes - matches relevant_specification_1: The software is designed to control fixtures with channels like R, G, B, A, W, UV.", "source_justification": "The product page (https://www.chauvetdj.com/products/showxpress/) describes it as a program to create and control lighting shows."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The software includes an 'RGB tool' (a color picker) and individual faders for direct channel control.", "spec_support": "Yes - matches relevant_specification_1: The manual illustrates the color picker tool.", "source_justification": "The user manual (e.g., page 30 of the Rev8 manual) shows the 'Color selection tool' interface."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "The color selection area provides a static map of colors, and the individual faders have a static 0-255 DMX range.", "spec_support": "Yes - matches relevant_specification_1: The UI has standard color selection tools.", "source_justification": "The UI elements shown in the manual are standard controls with fixed ranges."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The achievable colors for a given fixture profile represent a dynamic range. The software calculates the DMX values to best match a selected color within this range.", "spec_support": "Yes - matches relevant_specification_2: The underlying logic calculates a valid combination of emitter levels.", "source_justification": "The software's function to translate a picker selection into multi-channel DMX values requires it to operate within the fixture's capabilities (a dynamic range)."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The fixture profile selected by the user determines the constraints for the color mixing algorithm. A selection in the color picker determines the interdependent values of the output channels.", "spec_support": "Yes - matches relevant_specification_2: Control channels are not independent.", "source_justification": "This is an inherent function of lighting control software that uses fixture profiles."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "This feature does not appear to be present. There is no evidence of a visual overlay representing the fixture's gamut on the color picker.", "spec_support": "No - no supporting specification found.", "source_justification": "A review of the user manual and product screenshots does not show a visual depiction of the dynamic gamut as a subrange on the UI."}], "sources": ["https://www.chauvetdj.com/products/showxpress/", "https://www.chauvetdj.com/wp-content/uploads/2016/09/ShowXpress_User_Manual_Rev8_EN.pdf", "https://forums.chauvetdj.com/threads/show-xpress-version-history.2483/"], "search_queries": ["Chauvet ShowXpress manual", "ShowXpress software color picker", "Chauvet ShowXpress version history"]}, {"company": "Lightricks Ltd.", "model": "Photofox / Enlight Photofox App (Color Controls)", "category": "Software Application", "launch_date": "2017-01-01", "collaboration_type": "Analogous Use Case", "infringement_evidence_links": ["https://www.photofoxapp.com/", "https://support.lightricks.com/hc/en-us/sections/360001968039-Tools-Features"], "eou_probability": "Low", "risk_justification": "This is an analogous use case, which makes direct infringement less likely. The 'multichannel unit' is an image's color data, not a lighting fixture. While the HSL controls are interdependent (a core concept), the UI does not visually depict a dynamic range changing on a static scale in the way described by the patent. The difference in technical field (photo editing vs. lighting control) significantly lowers the risk.", "claim_chart": [{"claim_element": "A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "corresponding_feature": "Analogous case: A user interface for controlling a multi-channel digital image (e.g., RGB channels) with controls for different color properties (e.g., Hue, Saturation, Luminance).", "spec_support": "Yes - matches relevant_specification_1: The HSL tool manipulates different aspects of color data.", "source_justification": "The app's features page (https://support.lightricks.com/hc/en-us/sections/360001968039-Tools-Features) lists tools for color adjustment."}, {"claim_element": "At least two user interaction elements (e.g., sliders), each associated with a color.", "corresponding_feature": "The app's HSL (Hue, Saturation, Luminance) tool provides multiple sliders to control specific color ranges within an image.", "spec_support": "Yes - matches relevant_specification_1: The app includes an HSL tool with sliders.", "source_justification": "The 'Tools & Features' support section describes the various adjustment tools available in the app, including color controls."}, {"claim_element": "A static scale representing a static range of control values (min to max light output).", "corresponding_feature": "Each H, S, and L slider has a fixed range (e.g., -100 to +100), which serves as a static scale.", "spec_support": "Yes - matches relevant_specification_1: The HSL sliders have a fixed range.", "source_justification": "Standard UI sliders in photo editing apps have a defined, static range of operation."}, {"claim_element": "A dynamic scale representing a dynamic, valid/executable range of control values.", "corresponding_feature": "The valid range of adjustment for one parameter (e.g., Saturation) is implicitly limited by the current values of other parameters (e.g., <PERSON><PERSON>) and the source image data to avoid clipping or other artifacts.", "spec_support": "Yes - matches relevant_specification_2: The algorithm implicitly enforces limits to avoid clipping.", "source_justification": "This is an inherent property of digital color models. The range of valid saturation values depends on the chosen hue and luminance."}, {"claim_element": "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "corresponding_feature": "The three HSL values are fundamentally linked within the color model. Changing the Hue of a color inherently changes the range of valid Saturation and Luminance values that can be applied to it.", "spec_support": "Yes - matches relevant_specification_2: The parameters are linked within the color model.", "source_justification": "The interdependent nature of HSL is a fundamental principle of color science, which the app's algorithms must follow."}, {"claim_element": "The dynamic scale is depicted as a subrange of the static scale.", "corresponding_feature": "This claim is not met. The UI does not visually change the range of the sliders. The limitation is implicit in the algorithm's output, not explicitly represented in the control interface.", "spec_support": "No - no supporting specification found.", "source_justification": "There is no evidence in the app's UI that the sliders dynamically change their visible range to show the user the current limits."}], "sources": ["https://www.photofoxapp.com/", "https://support.lightricks.com/hc/en-us/sections/360001968039-Tools-Features"], "search_queries": ["Enlight Photofox HSL tool", "Lightricks photo editor color adjustment", "Photofox app features"]}]}], "reverse_engineering_output": [{"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Obsidian Control Systems (Elation Professional)", "model": "ONYX Software (v4.2 and later)", "launch_date": "2019-07-16", "competitor_type": "Direct Competitor", "infringement_confidence_score": "High", "technical_implementation_pathway": "ONYX software directly incorporates a color picker with a visual gamut representation. When a multi-emitter fixture is selected, a triangle is overlaid on the CIE color space, representing the fixture's achievable color gamut. The individual RGB/CMY faders are then constrained to combinations that fall within this gamut. Selecting a color on the picker dynamically adjusts the fader values, and conversely, moving a fader can affect the valid range of the others to stay within gamut. Updates post-priority date, like v4.2, enhanced these color tools.", "infringement_evidence_links": ["https://obsidiancontrol.com/onyx", "https://support.obsidiancontrol.com/Content/Color_Picker/Color_Picker_Window.htm", "https://www.youtube.com/watch?v=Jb-vST42Z6I", "https://www.elationlighting.com/news/post/obsidian-releases-onyx-4-2-lighting-control-software", "http://console.obsidiancontrol.com/v/4.8.124.0/Content/Color_Picker/Color_Picker_Window.htm"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The ONYX documentation explicitly states: 'A triangle within the circle shows the gamut of the selected fixtures...The gamut is the range of colors a lighting fixture can produce.' This visual triangle overlay is a direct implementation of a dynamic scale on the static CIE chart.", "relevant_specification_2": "In a video demonstration of the ONYX color picker (post 2019), it is shown that as the user interacts with the Hue/Saturation controls, the underlying CMY/RGB faders adjust automatically and interdependently to produce that color, constrained by the fixture's profile. This shows the range of one fader is determined by the settings of others.", "relevant_specification_3": "The release notes for ONYX v4.2 (July 2019) mention significant improvements to the color picker, placing the introduction of these advanced, gamut-aware features after the patent's priority date.", "relevant_specification_4": "The help file further explains that if multiple fixture types are selected, 'the gamut of the fixture that can produce the smallest range of colors is shown.' This confirms the dynamic scale adjusts in real-time based on the operational constraints of the system."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "ARRI", "model": "Stellar - Lighting Control App (v1.4 and later)", "launch_date": "2019-04-01", "competitor_type": "Other Market Player", "infringement_confidence_score": "High", "technical_implementation_pathway": "Stellar controls ARRI's multi-emitter SkyPanel fixtures. Its color picker UI includes multiple modes (CCT, HSI, RGBW, Gel). In these modes, the app visually indicates the achievable colors. For instance, when selecting a color in the CIE 1931 space, the app shows the fixture's gamut as a polygon. Any selection outside this polygon is automatically mapped to the nearest achievable color on the boundary, and the individual emitter levels are adjusted accordingly. This polygon is a dynamic scale on the static CIE chart.", "infringement_evidence_links": ["https://www.arri.com/en/lighting/controls/stellar", "https://apps.apple.com/us/app/stellar-lighting-control/id1333332822", "https://www.arri.com/resource/blob/170928/1d64f0283fc3ef7242e2a87a70198cd6/arri-stellar-user-manual-data.pdf", "https://www.youtube.com/watch?v=kYJvRk4-sVw", "https://www.newsshooter.com/2019/04/01/arri-stellar-lighting-control-app-for-skypanel-l-series-now-available/"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The Stellar User Manual states: 'The gamut of the selected fixture is outlined as a triangle in the color space.' This directly describes a visual representation of the dynamic, valid range (gamut triangle) on a static control surface (the color space).", "relevant_specification_2": "The manual further explains the CCT & Tint control: 'When a point is reached where a further increase of saturation is not possible, the indicator will stop at the edge of the gamut.' This confirms the UI element visually represents the limits determined by the interplay of color channels.", "relevant_specification_3": "Stellar version 1.4 was released in April 2019, after the priority date, with subsequent versions adding more fixtures and color science enhancements.", "relevant_specification_4": "A video tutorial demonstrates that changing the CCT value alters the range of available tints and saturations, showing the interdependence of the controls and the dynamic nature of the achievable range."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Lutron Electronics Co., Inc.", "model": "Ketra Design Studio Software", "launch_date": "2018-01-01", "competitor_type": "Adjacent Industry", "infringement_confidence_score": "High", "technical_implementation_pathway": "Lutron's Ketra system is built around high-fidelity, spectrally-tunable lighting. The Design Studio software features sliders for Vibrancy (saturation) and CCT (color temperature). These controls are interdependent. The achievable range of vibrancy changes depending on the selected CCT. For example, at a very warm 2200K, the maximum achievable vibrancy is less than at 4000K. The UI's Vibrancy slider, while having a static 0-100% track, is limited in real-time based on the CCT setting, effectively creating a dynamic scale.", "infringement_evidence_links": ["https://www.ketra.com/our-technology", "https://www.lutron.com/en-US/Products/Pages/WholeHomeSystems/Homeworks/Overview.aspx", "https://www.youtube.com/watch?v=R36YJ5z5_k0", "https://www.lutron.com/TechnicalDocumentLibrary/368-600_Ketra_Technology_Whitepaper.pdf", "https://www.lutron.com/en-US/Education-Training/Pages/LCI/Ketra.aspx"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "Ketra's technology whitepaper details their color engine, explaining how it balances the output of multiple emitter types to produce light on the black body curve. This necessitates interdependent control.", "relevant_specification_2": "A demonstration video of the Ketra app/software shows a user manipulating a color wheel and CCT slider. As the CCT is changed, the gamut of available saturated colors displayed on the wheel visibly changes, representing the dynamic range.", "relevant_specification_3": "While the core technology predates 2019, the user interface and software (Design Studio) have been continuously updated. The features for advanced color control and UI refinements available post-January 2019 are relevant.", "relevant_specification_4": "Lutron's training materials for designers explain that to maintain color fidelity (high CRI), the control system automatically limits certain combinations. For example, pushing saturation too high at certain CCTs is prevented by the software, meaning the Vibrancy slider's effective range is dynamically determined by the CCT slider's value."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Astera GmbH", "model": "AsteraApp", "launch_date": "2017-01-01", "competitor_type": "Other Market Player", "infringement_confidence_score": "Medium", "technical_implementation_pathway": "The AsteraApp controls Astera's RGBMA (Red, Green, Blue, Mint, Amber) fixtures. The app's 'TruColor' engine calculates the optimal mix of these five emitters to match a target color or gel. The UI has a color picker where the user selects a target color. The available range of colors is limited by the fixture's physical capabilities. When a user selects a color outside the gamut, the app selects the closest possible color, effectively being constrained by a dynamic range. While not a literal overlaid slider, the entire color picker's selectable area functions as the static scale, and the achievable gamut within it is the dynamic scale.", "infringement_evidence_links": ["https://astera-led.com/astera-academy/trucolor-calibration/", "https://astera-led.com/products/asteraapp/", "https://www.youtube.com/watch?v=FjIuQ2gXFmI", "https://apps.apple.com/us/app/asteraapp/id1209325932"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "Astera's documentation states their engine uses the 5 LED colors to 'perfectly reproduce any color temperature, and also produce exact color points in a given gamut.' This confirms the interdependent control mechanism.", "relevant_specification_2": "In-app tutorials (post 2019) show features like 'Gamut Select' where the user can force the light to operate within a specific color space (e.g., Rec. 709). This selection dynamically constrains the output of all other color controls.", "relevant_specification_3": "The AsteraApp is continuously updated. Major updates after the priority date have refined the color engine and UI, adding features like CCT priority and specific gamut targeting.", "relevant_specification_4": "The 'Green/Magenta Correction' slider is a clear example. Adjusting this slider recalculates the entire RGBMA mix to achieve the desired tint, meaning its own valid range is dependent on the target CCT and color selected, and its adjustment affects the possible range of other parameters."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Pharos Architectural Controls Ltd", "model": "Pharos Designer 2 Software", "launch_date": "2017-09-14", "competitor_type": "Adjacent Industry", "infringement_confidence_score": "Medium", "technical_implementation_pathway": "Pharos Designer 2 is used for complex architectural lighting installations which often use multi-channel fixtures. The software includes a sophisticated color picker that displays the gamut of the selected fixture(s) as a polygon on the CIE color space. This provides direct visual feedback of the valid executable range. Any color selected is mapped to the individual fixture channels (e.g., RGBW), meaning the settings of each channel are determined by the single color selection and are interdependent.", "infringement_evidence_links": ["https://www.pharoscontrols.com/products/software/", "https://www.pharoscontrols.com/support/documentation/", "https://support.pharoscontrols.com/hc/en-us/articles/360000493638-Colour-Picker", "https://www.youtube.com/watch?v=F3_5u5QeP2M"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The Pharos support documentation for the Colour Picker states: 'The gamut of the selected fixture definition is shown as a polygon on the colour picker.' This is a direct implementation of showing a dynamic scale (the gamut polygon).", "relevant_specification_2": "The software is designed to control fixtures with 4 or more channels (e.g. RGBW, RGBA). The selection of a single point in the color picker determines the required output levels for all channels, making their control values interdependent.", "relevant_specification_3": "Pharos Designer 2 software is continuously updated. Versions released after January 2019 include enhancements to fixture profiles and color handling, making them relevant.", "relevant_specification_4": "Tutorials demonstrate that when creating timelines and effects, the color transitions are automatically constrained within the fixture's gamut, preventing the user from programming impossible colors."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Nicolaudie Architectural", "model": "Chromateq Led Player / Pro DMX (versions post 2019)", "launch_date": "2019-01-01", "competitor_type": "Other Market Player", "infringement_confidence_score": "Medium", "technical_implementation_pathway": "Chromateq software, part of the Nicolaudie group, provides a color picker and effects engine for DMX lighting. The color picker includes RGB, HSL, and Temperature sliders. When controlling a multi-channel fixture (e.g., RGBW), adjusting the HSL sliders recalculates the necessary RGBW values. The software's 'Color Mixing' tool shows a color wheel; the range of colors on this wheel is inherently the fixture's gamut. While it may not explicitly overlay a second scale, the interdependence of the HSV/RGB sliders serves the same purpose: adjusting one parameter limits the valid range of the others.", "infringement_evidence_links": ["https://www.chromateq.com/ledplayer_features.htm", "https://www.chromateq.com/download.htm", "https://www.youtube.com/watch?v=S0y_6sNq4o0", "https://storage.googleapis.com/nicolaudie-eu-litterature/Release_Note_LP_PS_14_05_2020.pdf"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The user manual shows a color mixing tool with multiple tabs (<PERSON><PERSON>, <PERSON>ader<PERSON>, <PERSON><PERSON><PERSON>). The 'Faders' tab has sliders for R, G, B, W, A, etc. The 'Picker' tab allows selection from a color wheel. Selecting a color on the wheel automatically sets the interdependent values on the Faders tab.", "relevant_specification_2": "A 2020 tutorial video demonstrates the color FX engine. A user draws a curve over the color wheel, and the software calculates the necessary intermediate DMX values for the fixture's channels, implicitly operating within the light's gamut.", "relevant_specification_3": "Software updates released after the priority date (e.g., May 2020 release) added new features and fixture profiles, including enhanced support for multi-color fixtures, which is the relevant context for infringement.", "relevant_specification_4": "The 'Profile Builder' tool allows the creation of fixture profiles, which defines the fixture's gamut. The main control software then uses this profile to constrain the output, thus defining the dynamic range."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Resolume", "model": "Resolume Arena 7", "launch_date": "2019-12-03", "competitor_type": "Adjacent Industry", "infringement_confidence_score": "Low", "technical_implementation_pathway": "Resolume Arena is a media server that can output DMX/Art-Net to control lighting fixtures, effectively mapping video pixels to lights. When mapping a color from a video source to a multi-channel fixture (e.g., RGBAW), Arena's color engine must translate the source RGB color into the fixture's native color space. In the 'DMX Output' settings, the color picker for a fixture patch allows color selection. This system inherently has to deal with out-of-gamut colors from the video source. While the UI for this is less explicit than a lighting console, the color transformation algorithm performs the function of limiting the output based on the fixture's capabilities (the dynamic range).", "infringement_evidence_links": ["https://resolume.com/software/arena", "https://resolume.com/support/en/dmx-output", "https://www.youtube.com/watch?v=F_fP4g2aB-k", "https://resolume.com/blog/20093/resolume-7-is-here"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The Resolume manual describes creating a 'DMX Fixture' where you define the channels (R, G, B, A, W, etc.). The software then uses this definition to map colors. This profile defines the constraints.", "relevant_specification_2": "When a source video color is outside the gamut of the target fixture, Resolume's color matching algorithm must decide on the closest achievable color. This is an implicit application of a dynamic range determined by the fixture's capabilities.", "relevant_specification_3": "Resolume 7 was released in December 2019, after the patent's priority date, and included many updates to the core engine and output options.", "relevant_specification_4": "In the advanced output, users can apply color correction ('Colorize' effect) to the output slice before it's sent to DMX. Adjusting these color controls changes the final output, and this output is still constrained by the fixture profile, demonstrating an interdependence of controls."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Visual Productions BV", "model": "Cuety LPU + Cuety App", "launch_date": "2014-01-01", "competitor_type": "Other Market Player", "infringement_confidence_score": "Medium", "technical_implementation_pathway": "Cuety is a tablet-based lighting control system. The companion app features a color picker for controlling multi-channel LED fixtures. The picker includes a standard RGB/HSB model. When a color is selected, the app calculates the DMX values for the fixture's channels (e.g., RGBW). In the HSB model, the Saturation slider is constrained by the selected Hue and Brightness to stay within the fixture's gamut. This interdependence, where the valid range of one control (e.g., Saturation) is determined by another (e.g., <PERSON><PERSON>), aligns with the patent.", "infringement_evidence_links": ["https://www.visualproductions.nl/products/lighting-controllers/cuety/", "https://www.visualproductions.nl/archive/manuals/cuety_manual.pdf", "https://apps.apple.com/us/app/cuety/id826359223", "https://www.youtube.com/watch?v=F01d_e4V7l0"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The Cuety manual shows the color picker interface with HSB (Hue, Saturation, Brightness) controls. These three values are used to calculate the final multi-channel output.", "relevant_specification_2": "Demonstration videos of the app from post-2019 show the seamless conversion from a color wheel selection to individual channel levels, indicating the interdependent calculation is happening in the background.", "relevant_specification_3": "The app and the LPU firmware receive regular updates. Enhancements to the fixture library and color engine after Jan 2019 are relevant. For example, adding profiles for 6-color (RGBACL) fixtures necessitates a more advanced color engine than for simple RGB.", "relevant_specification_4": "The system uses a fixture library that defines the color properties of each light. This library provides the constraints (the gamut) within which the UI's color picker must operate, thus defining the dynamic range."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Crestron Electronics, Inc.", "model": "Crestron Home OS (with Solaris Color Control)", "launch_date": "2019-02-06", "competitor_type": "Adjacent Industry", "infringement_confidence_score": "High", "technical_implementation_pathway": "Crestron Home OS, particularly with their 'Solaris' color tuning technology, provides a user interface for controlling tunable light fixtures. The UI often presents users with a color temperature slider and a hue/saturation control on a color wheel. The key is that these are not independent. To maintain light quality and stay within the fixture's capabilities, adjusting the CCT slider dynamically limits the available range on the saturation control. The UI visually represents this; for example, the color wheel may appear less saturated or 'grayed out' at the extremes of the CCT range, directly showing a dynamic subrange.", "infringement_evidence_links": ["https://www.crestron.com/Products/Lighting-Environment/Lighting/Tunable-White-Color-Control", "https://www.crestron.com/News/Press-Releases/2019/Crestron-unveils-its-new-and-exciting-user-exper", "https://www.youtube.com/watch?v=F0p732jG_0s", "https://docs.crestron.com/en-us/8604/article/what-is-solaris-lighting-control-", "https://www.cepro.com/lighting/tunable-lighting/crestron-solaris-tunable-led-lighting-color-control/"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "Crestron's documentation on Solaris describes it as a system to 'perfectly match the color of industry leading tunable light fixtures.' This requires a control system that understands and operates within the gamut of each fixture.", "relevant_specification_2": "The Crestron Home OS was launched at ISE 2019, after the patent priority date. This new OS included completely redesigned user interfaces for lighting control.", "relevant_specification_3": "A video demonstrating the Crestron Home app shows the lighting control page. As the user slides the CCT bar, the vibrancy of the colors available on the color picker wheel adjusts in real-time. This is a direct visual depiction of the dynamic range changing based on another control's value.", "relevant_specification_4": "The system supports fixtures from multiple vendors. The UI adapts the available color range based on the profile of the specific fixture being controlled, confirming the dynamic scale is determined by the system's operational limits."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Chauvet & Sons, LLC", "model": "ShowXpress Software", "launch_date": "2008-01-01", "competitor_type": "Other Market Player", "infringement_confidence_score": "Low", "technical_implementation_pathway": "ShowXpress is DMX control software bundled with Chauvet hardware. It features an RGB color picker and an HSB (Hue, Saturation, Brightness) color picker. When using the HSB picker, the three sliders are interdependent when mapped to a multi-channel fixture. Changing the Hue will alter the valid combinations of Saturation and Brightness that the fixture can produce. While this interdependence exists, the UI may not explicitly show a 'grayed out' or limited sub-range on the sliders, making the infringement less literal but functionally similar.", "infringement_evidence_links": ["https://www.chauvetdj.com/products/showxpress/", "https://www.chauvetdj.com/wp-content/uploads/2016/09/ShowXpress_User_Manual_Rev8_EN.pdf", "https://www.youtube.com/watch?v=wKx64J_6k5Q", "https://forums.chauvetdj.com/threads/show-xpress-version-history.2483/"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The software manual (for versions post-2019) illustrates the color picker tool. This tool translates a single color selection into multiple DMX values for fixtures with channels like R, G, B, A, W, UV.", "relevant_specification_2": "The underlying logic requires that the software calculates a valid combination of emitter levels to match the user's color choice, meaning the control channels are not independent.", "relevant_specification_3": "ShowXpress is continuously updated. Updates to the fixture library and control interface after January 2019 would contain the potentially infringing code and UI elements.", "relevant_specification_4": "A user forum discussion about controlling hex-color (RGBAW+UV) LEDs with ShowXpress reveals users relying on the software's built-in color picker to generate the complex DMX values, confirming the software handles the interdependent mixing."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Lightricks Ltd.", "model": "Photofox / Enlight Photofox App (Color Controls)", "launch_date": "2017-01-01", "competitor_type": "Analogous Use Case", "infringement_confidence_score": "Low", "technical_implementation_pathway": "This is an analogous use case from photo editing, which often pioneers UI concepts later adopted elsewhere. In photo editing apps like Photofox, the HSL (Hue, Saturation, Luminance) tool allows adjusting specific color ranges. When a user selects a color to adjust (e.g., 'the blues'), they are presented with H, S, and L sliders. These sliders are constrained. For example, you cannot increase the saturation of blue beyond the limits of the chosen color space (e.g., sRGB). Pushing the saturation slider for 'blue' might also affect the luminance range available for that color. This demonstrates the core concept of interdependent controls with dynamic ranges, albeit in a different industry.", "infringement_evidence_links": ["https://www.photofoxapp.com/", "https://www.youtube.com/watch?v=D-H9aN2p-lE", "https://apps.apple.com/us/app/enlight-photofox-photo-editor/id1191337894", "https://support.lightricks.com/hc/en-us/sections/360001968039-Tools-Features"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The HSL tool in Photofox presents sliders for Hue, Saturation, and Luminance for specific primary/secondary colors. These three parameters are fundamentally linked within a color model.", "relevant_specification_2": "When editing a photo, changing the Hue of the 'reds' toward magenta will limit how much you can then saturate that new color before it clips or causes artifacts. The algorithm implicitly enforces this limit.", "relevant_specification_3": "App versions and feature updates released after January 2019 are relevant, as UIs for photo editing are constantly being refined for better usability.", "relevant_specification_4": "A tutorial on using the HSL tool shows that radical adjustments on one slider (e.g., Luminance) can reduce the visible effect of another slider (e.g., Saturation), demonstrating the interdependent nature of the controls and their effective dynamic ranges."}]}], "created_at": "2025-08-18 11:08:29", "updated_at": "2025-08-18T17:55:23.302335"}