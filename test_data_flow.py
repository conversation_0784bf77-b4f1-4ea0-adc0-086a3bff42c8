#!/usr/bin/env python3
"""
Test script to verify the data flow between agents
"""
import json
from models import InfringementRequest, ReverseEngineeringRequest, ClaimChartRequest, SpecSupportItem, KeyConceptItem

def test_data_structures():
    """Test that the new data structures work correctly"""
    
    # Sample spec support data (from novelty agent)
    spec_support = [
        SpecSupportItem(
            key_feature="A user interface (100) for controlling a multichannel lighting unit",
            relevant_specification_1="Described herein is a user interface for controlling a multichannel lighting unit...",
            relevant_specification_2="Figures 1A and 1B each disclose 2 user interaction elements...",
            relevant_specification_3="The color associated with a user interaction elements does not necessarily..."
        ),
        SpecSupportItem(
            key_feature="characterized in that the dynamic range of control values",
            relevant_specification_1="The dynamic range of a first of the at least two user interaction elements...",
            relevant_specification_2="Control values may include luminous/radiant power...",
            relevant_specification_3="In general, the dynamic range of a user interaction elements..."
        )
    ]
    
    # Test infringement request structure
    infringement_req = InfringementRequest(
        patent_number="EP3915337B1",
        priority_date="2019-01-21",
        assignees=["Signify Holding BV"],
        grant_date="2023-11-15",
        application_date="2020-01-17",
        expiration_date="2040-01-17",
        abstract="Dynamic user interface",
        classifications=["H05B47/175"],
        inventors=["Roelf Melis VAN DER SLOOT"],
        patent_family=["EP3915337B1", "US11490493B2"],
        competitors=["EEMA Industries", "ETI Solid State Lighting"],
        keyconcepts=[KeyConceptItem(keyfeature="test", keyconcept="test concept")],
        keyfeature_list=["Feature 1", "Feature 2"],
        spec_support=spec_support,
        forward_citation_assignees=["Shopify Inc."],
        novelty_summary="Dynamic user interface with interdependent controls"
    )
    
    print("✓ InfringementRequest structure validated")
    
    # Simulate infringement agent response
    infringement_response = {
        "products": [
            {
                "patent_number": "EP3915337B1",
                "assignees": ["Signify Holding BV"],
                "priority_date": "2019-01-21",
                "novelty_summary": "Dynamic user interface with interdependent controls",
                "keyfeature_list": ["Feature 1", "Feature 2"],
                "company": "Test Company",
                "model": "Test Product",
                "launch_date": "2020-01-01",
                "competitor_type": "Direct Competitor",
                "infringement_evidence_links": ["https://example.com/spec"],
                "spec_support": [{"key_feature": "simplified", "relevant_specification_1": "simplified spec"}]
            }
        ],
        "spec_support": [item.model_dump() for item in spec_support]  # Complete original spec support
    }
    
    print("✓ Infringement response structure validated")
    
    # Test reverse engineering request
    reverse_eng_req = ReverseEngineeringRequest(
        products=[],  # Would be populated from infringement response
        spec_support=spec_support
    )
    
    print("✓ ReverseEngineeringRequest structure validated")
    
    # Test claim chart request
    claim_chart_req = ClaimChartRequest(
        products=[],  # Would be populated from reverse engineering response
        spec_support=spec_support
    )
    
    print("✓ ClaimChartRequest structure validated")
    
    # Test backward compatibility
    assert hasattr(reverse_eng_req, 'infringed_models'), "Backward compatibility missing"
    assert hasattr(claim_chart_req, 'infringed_models'), "Backward compatibility missing"
    
    print("✓ Backward compatibility validated")
    
    # Test spec support preservation
    assert len(claim_chart_req.spec_support) == 2, "Spec support not preserved"
    assert claim_chart_req.spec_support[0].key_feature == "A user interface (100) for controlling a multichannel lighting unit"
    
    print("✓ Spec support preservation validated")
    
    print("\n🎉 All data structure tests passed!")
    
    return {
        "infringement_request": infringement_req.model_dump(),
        "infringement_response": infringement_response,
        "reverse_engineering_request": reverse_eng_req.model_dump(),
        "claim_chart_request": claim_chart_req.model_dump()
    }

def validate_spec_support_flow():
    """Validate that spec support flows correctly through the pipeline"""
    
    print("\n=== Spec Support Flow Validation ===")
    
    # Original spec support from novelty agent
    original_spec_support = [
        {
            "key_feature": "A user interface (100) for controlling a multichannel lighting unit (530)",
            "relevant_specification_1": "Described herein is a user interface for controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source...",
            "relevant_specification_2": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue...",
            "relevant_specification_3": "The color associated with a user interaction elements does not necessarily directly associate with or relate to a channel..."
        },
        {
            "key_feature": "characterized in that the dynamic range of control values is a valid/executable range",
            "relevant_specification_1": "The dynamic range of a first of the at least two user interaction elements is automatically adapted when the present control value...",
            "relevant_specification_2": "Control values may include luminous/radiant power or luminous/radiant flux, luminous/radiant intensity...",
            "relevant_specification_3": "In general, the dynamic range of a user interaction elements will be a subrange of the static range..."
        }
    ]
    
    print(f"✓ Original spec support has {len(original_spec_support)} detailed entries")
    
    # Verify each entry has complete specifications
    for i, entry in enumerate(original_spec_support):
        assert "key_feature" in entry, f"Entry {i} missing key_feature"
        assert "relevant_specification_1" in entry, f"Entry {i} missing relevant_specification_1"
        assert len(entry["relevant_specification_1"]) > 50, f"Entry {i} specification_1 too short"
        print(f"  ✓ Entry {i+1}: {entry['key_feature'][:50]}...")
    
    print("✓ All spec support entries have detailed specifications")
    
    # Simulate the flow through agents
    print("\n--- Simulating Agent Flow ---")
    
    # 1. Infringement agent should preserve complete spec support
    infringement_output = {
        "products": [{"company": "Test", "model": "Product1"}],
        "spec_support": original_spec_support  # MUST be preserved completely
    }
    
    assert infringement_output["spec_support"] == original_spec_support
    print("✓ Infringement agent preserves complete spec support")
    
    # 2. Reverse engineering agent should preserve complete spec support
    reverse_eng_output = {
        "products": infringement_output["products"] + [{"company": "Test2", "model": "Product2"}],
        "spec_support": original_spec_support  # MUST be preserved completely
    }
    
    assert reverse_eng_output["spec_support"] == original_spec_support
    print("✓ Reverse engineering agent preserves complete spec support")
    
    # 3. Claim chart agent should use complete spec support for ALL products
    claim_chart_input = {
        "products": reverse_eng_output["products"],
        "spec_support": original_spec_support
    }
    
    # Verify claim chart agent has access to complete spec support
    assert len(claim_chart_input["spec_support"]) == 2
    assert len(claim_chart_input["products"]) == 2
    
    print("✓ Claim chart agent receives complete spec support for all products")
    
    # Verify that claim chart should map each product to each key feature
    expected_mappings = len(claim_chart_input["products"]) * len(claim_chart_input["spec_support"])
    print(f"✓ Expected claim chart mappings: {expected_mappings} (2 products × 2 key features)")
    
    print("\n🎉 Spec support flow validation passed!")

if __name__ == "__main__":
    test_data_structures()
    validate_spec_support_flow()
    print("\n✅ All tests passed! Data flow is properly configured.")
