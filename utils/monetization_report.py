"""
Monetization Report Generator Tool
Creates professional monetization reports in Excel format following the Wissen Research sample format
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from google.adk.tools import FunctionTool

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def set_cell_style(cell, bold=False, fill=None, alignment=None, border=None, font_size=None):
    """Helper function to apply styles to a cell."""
    if bold:
        cell.font = Font(bold=True, size=font_size or 11)
    elif font_size:
        cell.font = Font(size=font_size)
    if fill:
        cell.fill = fill
    if alignment:
        cell.alignment = alignment
    if border:
        cell.border = border


def create_monetization_report_excel(patent_json: str, products_json: str, output_file: str = None) -> str:
    """
    Creates a comprehensive monetization report in Excel format from patent analysis data.
    The format closely matches the Wissen Research sample report structure.

    Args:
        patent_json: A JSON string containing the patent data from your API.
        products_json: A JSON string containing the list of infringing products.
        output_file: The desired filename for the output Excel file.

    Returns:
        A string indicating the success or failure of the operation.
    """
    try:
        patent_data = json.loads(patent_json)
        products_data = json.loads(products_json)

        # Generate default output filename if not provided
        if not output_file:
            patent_number = patent_data.get('patent_number', 'unknown')
            output_file = f"{patent_number}_Monetization_Report.xlsx"

        wb = Workbook()
        ws = wb.active
        ws.title = "Monetization Report"

        # --- Styles ---
        header_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
        light_blue_fill = PatternFill(start_color="E7F3FF", end_color="E7F3FF", fill_type="solid")
        thin_border = Border(left=Side(style='thin'), right=Side(style='thin'),
                             top=Side(style='thin'), bottom=Side(style='thin'))

        current_row = 1

        # --- Main Header ---
        ws[f'A{current_row}'] = "AI Agent-Powered Patent Monetization Report"
        ws.merge_cells(f'A{current_row}:G{current_row}')
        set_cell_style(ws[f'A{current_row}'], bold=True, font_size=16,
                       alignment=Alignment(horizontal='center', vertical='center'))
        ws.row_dimensions[current_row].height = 30
        current_row += 2

        # --- Patent Overview Section ---
        ws[f'A{current_row}'] = "Patent Overview"
        set_cell_style(ws[f'A{current_row}'], bold=True, font_size=14)
        current_row += 1

        # Extract patent data from different possible structures
        novelty_data = patent_data.get('novelty_data', {})

        # Patent overview data with bullet points
        overview_items = [
            ("Patent Number:", patent_data.get('patent_number', 'N/A')),
            ("Title:", novelty_data.get('title', patent_data.get('title', 'N/A'))),
            ("Assignees:", novelty_data.get('assignees', patent_data.get('assignees', 'N/A'))),
            ("Inventor(s):", novelty_data.get('inventors', 'N/A')),
            ("Earliest Priority Date:", novelty_data.get('priority_date', patent_data.get('priority_date', 'N/A'))),
            ("Filed Date:", novelty_data.get('application_date', patent_data.get('application_date', 'N/A'))),
            ("Grant Date:", novelty_data.get('grant_date', patent_data.get('grant_date', 'N/A'))),
            ("Expiration Date:", novelty_data.get('expiration_date', patent_data.get('expiration_date', 'N/A'))),
            ("Forward Citations:", str(len(
                novelty_data.get('forward_citation_assignees', patent_data.get('forward_citation_assignees', []))))),
            ("Backward Citations:",
             str(len(novelty_data.get('backward_citations', patent_data.get('backward_citations', []))))),
            ("Patent Family:", novelty_data.get('patent_family', 'N/A')),
            ("Classifications:", "")
        ]

        for label, value in overview_items:
            ws[f'A{current_row}'] = f"• {label}"
            set_cell_style(ws[f'A{current_row}'], bold=True)
            ws[f'B{current_row}'] = value
            current_row += 1

        # Add classifications sub-items
        classifications = novelty_data.get('classifications', patent_data.get('classifications', []))
        if classifications:
            current_row -= 1  # Go back to Classifications line
            ws[f'B{current_row}'] = ""
            current_row += 1

            # Extract IPC and CPC codes
            ipc_codes = []
            cpc_codes = []

            for classification in classifications:
                code = classification.get('code', '')
                if code.startswith('G06F') or code.startswith('H04') or code.startswith('G09'):
                    ipc_codes.append(code)
                else:
                    cpc_codes.append(code)

            if ipc_codes:
                ws[f'B{current_row}'] = f"- IPC: {', '.join(ipc_codes)}"
                current_row += 1
            if cpc_codes:
                ws[f'B{current_row}'] = f"- CPC: {', '.join(cpc_codes)}"
                current_row += 1

            # Add classification descriptions
            current_row += 1
            ws[f'A{current_row}'] = "Classification Descriptions:"
            set_cell_style(ws[f'A{current_row}'], bold=True)
            current_row += 1

            for classification in classifications:
                desc_text = f"• {classification.get('code', 'N/A')}: {classification.get('description', 'N/A')}"
                ws[f'A{current_row}'] = desc_text
                ws[f'A{current_row}'].alignment = Alignment(wrap_text=True)
                ws.merge_cells(f'A{current_row}:G{current_row}')
                current_row += 1

        current_row += 1

        # --- Calculate Infringement Score (simplified logic) ---
        # This is a placeholder - you can implement your own scoring logic
        infringement_score = 6  # Default medium score

        # Adjust score based on number of products and risk assessment
        high_risk_products = [p for p in products_data if p.get('eou_probability', '').lower() == 'high']
        if len(high_risk_products) >= 3:
            infringement_score = 8  # High score
        elif len(high_risk_products) >= 1:
            infringement_score = 7  # Medium-high score
        elif len(products_data) <= 1:
            infringement_score = 4  # Low score

        score_text = f"{infringement_score} ({'High' if infringement_score >= 8 else 'Medium' if infringement_score >= 5 else 'Low'})"

        # --- Infringement Score Section ---
        ws[f'A{current_row}'] = f"Infringement Score – {score_text}"
        set_cell_style(ws[f'A{current_row}'], bold=True, font_size=12)
        current_row += 1

        # Infringement score table
        score_headers = ["Patent No.", "Priority Date", "Forward Citations", "Backward Citations",
                         "Expiry Date", "Claim Length (First Claim)", "Infringement Score"]

        # Create header row
        for col_num, header in enumerate(score_headers, 1):
            cell = ws.cell(row=current_row, column=col_num)
            cell.value = header
            set_cell_style(cell, bold=True, fill=header_fill, border=thin_border,
                           alignment=Alignment(horizontal='center'))

        current_row += 1

        # Calculate claim length from first independent claim
        claim_length = 0
        independent_claims = novelty_data.get('independent_claims', patent_data.get('independent_claims', []))
        if independent_claims:
            first_claim = independent_claims[0]
            claim_length = len(first_claim)

        # Add score data
        score_data = [
            patent_data.get('patent_number', 'N/A'),
            novelty_data.get('priority_date', patent_data.get('priority_date', 'N/A')),
            str(len(novelty_data.get('forward_citation_assignees', patent_data.get('forward_citation_assignees', [])))),
            str(len(novelty_data.get('backward_citations', patent_data.get('backward_citations', [])))),
            novelty_data.get('expiration_date', patent_data.get('expiration_date', 'N/A')),
            str(claim_length),
            str(infringement_score)
        ]

        for col_num, value in enumerate(score_data, 1):
            cell = ws.cell(row=current_row, column=col_num)
            cell.value = value
            cell.border = thin_border
            cell.alignment = Alignment(horizontal='center')

        current_row += 1

        # Note about scoring
        ws[f'A{current_row}'] = "*Note: High:8-10, Medium:5-7, Low:1-4"
        set_cell_style(ws[f'A{current_row}'], font_size=10)
        current_row += 2

        # --- Core Invention Summary ---
        ws[f'A{current_row}'] = "Core Invention Summary"
        set_cell_style(ws[f'A{current_row}'], bold=True, font_size=12)
        current_row += 1

        # Use abstract or novelty summary as core invention summary
        summary_text = novelty_data.get('novelty_summary',
                                        novelty_data.get('abstract',
                                                         patent_data.get('abstract', 'N/A')))
        ws[f'A{current_row}'] = summary_text
        ws[f'A{current_row}'].alignment = Alignment(wrap_text=True)
        ws.merge_cells(f'A{current_row}:G{current_row + 2}')
        current_row += 4

        # --- Claim Summary ---
        ws[f'A{current_row}'] = "Claim Summary:"
        set_cell_style(ws[f'A{current_row}'], bold=True)
        current_row += 1

        # Extract first independent claim
        claim_summary = "N/A"
        independent_claims = novelty_data.get('independent_claims', patent_data.get('independent_claims', []))
        if independent_claims:
            claim_summary = independent_claims[0]

        ws[f'A{current_row}'] = claim_summary
        ws[f'A{current_row}'].alignment = Alignment(wrap_text=True)
        ws.merge_cells(f'A{current_row}:G{current_row + 2}')
        current_row += 4

        # --- Novel Point ---
        ws[f'A{current_row}'] = "Novel point from File Wrapper Analysis:"
        set_cell_style(ws[f'A{current_row}'], bold=True)
        current_row += 1

        # Extract novel point from novelty summary or generate from claim
        novel_point = novelty_data.get('key_novelty', '')
        if not novel_point and independent_claims:
            # Simple extraction of potential novel elements from first claim
            first_claim = independent_claims[0].lower()

            # Look for phrases that might indicate novelty
            novelty_indicators = [
                "comprising", "wherein", "characterized by", "such that",
                "configured to", "adapted to", "arranged to"
            ]

            for indicator in novelty_indicators:
                if indicator in first_claim:
                    parts = first_claim.split(indicator, 1)
                    if len(parts) > 1:
                        novel_point = parts[1].strip().split(",")[0].strip()
                        break

            if not novel_point:
                novel_point = "See claim 1 for novel elements"

        ws[f'A{current_row}'] = f'"{novel_point}"'
        current_row += 2

        # --- Monetization Strategy ---
        ws[f'A{current_row}'] = "Monetization Strategy"
        set_cell_style(ws[f'A{current_row}'], bold=True, font_size=12)
        current_row += 1

        ws[f'A{current_row}'] = "Potential Licensees:"
        set_cell_style(ws[f'A{current_row}'], bold=True)
        current_row += 1

        # Use forward citation assignees as potential licensees
        forward_assignees = novelty_data.get('forward_citation_assignees',
                                             patent_data.get('forward_citation_assignees', []))

        # Add unique companies from products as potential licensees
        product_companies = set(p.get('company', '') for p in products_data if p.get('company'))
        all_licensees = list(set(forward_assignees + list(product_companies)))

        if all_licensees:
            for assignee in all_licensees[:10]:  # Limit to first 10
                ws[f'A{current_row}'] = f"• {assignee}"
                current_row += 1
        current_row += 1

        # --- Enforcement Strategy ---
        ws[f'A{current_row}'] = "Enforcement Strategy"
        set_cell_style(ws[f'A{current_row}'], bold=True, font_size=12)
        current_row += 1

        ws[f'A{current_row}'] = "Potential Target Companies & their potential Infringing products:"
        set_cell_style(ws[f'A{current_row}'], bold=True)
        current_row += 1

        # --- Infringing Products Table ---
        table_headers = ["Company", "Infringing Product", "Revenue", "Launch Date"]

        # Create header row
        for col_num, header in enumerate(table_headers, 1):
            cell = ws.cell(row=current_row, column=col_num)
            cell.value = header
            set_cell_style(cell, bold=True, fill=header_fill, border=thin_border,
                           alignment=Alignment(horizontal='center'))

        current_row += 1

        # Add infringing products data
        for product in products_data:
            ws.cell(row=current_row, column=1, value=product.get('company', 'N/A')).border = thin_border
            ws.cell(row=current_row, column=2, value=product.get('model', 'N/A')).border = thin_border
            ws.cell(row=current_row, column=3, value="N/A").border = thin_border  # Revenue not provided

            # Handle different date field names
            launch_date = product.get('launch_date', product.get('launched_date', 'N/A'))
            ws.cell(row=current_row, column=4, value=launch_date).border = thin_border

            current_row += 1

        current_row += 1

        # --- Recommendations ---
        ws[f'A{current_row}'] = "Recommendations:"
        set_cell_style(ws[f'A{current_row}'], bold=True)
        current_row += 1

        recommendations = [
            "- We can create EoUs on \"High\" marked \"EoU Probability\" infringing products",
            "- Tear down needed for the \"Medium\" marked \"EoU Probability\" infringing products"
        ]

        for rec in recommendations:
            ws[f'A{current_row}'] = rec
            current_row += 1

        current_row += 1

        # --- EoU Probability Table ---
        ws[f'A{current_row}'] = "EoU Probability based on Claim Limitations and Product Information:"
        set_cell_style(ws[f'A{current_row}'], bold=True)
        current_row += 1

        eou_headers = ["Company", "Infringing Product", "EoU Probability", "Evidence Links"]

        # Create header row
        for col_num, header in enumerate(eou_headers, 1):
            cell = ws.cell(row=current_row, column=col_num)
            cell.value = header
            set_cell_style(cell, bold=True, fill=header_fill, border=thin_border,
                           alignment=Alignment(horizontal='center'))

        current_row += 1

        # Add EoU probability data
        for product in products_data:
            ws.cell(row=current_row, column=1, value=product.get('company', 'N/A')).border = thin_border
            ws.cell(row=current_row, column=2, value=product.get('model', 'N/A')).border = thin_border

            # Map eou_probability to EoU probability if not provided
            eou_prob = product.get('EoU probability', product.get('eou_probability', 'Medium'))
            cell = ws.cell(row=current_row, column=3, value=eou_prob)
            cell.border = thin_border

            # Color code based on probability
            if eou_prob.lower() == 'high':
                cell.fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")
            elif eou_prob.lower() == 'medium':
                cell.fill = PatternFill(start_color="FFFF99", end_color="FFFF99", fill_type="solid")
            elif eou_prob.lower() == 'low':
                cell.fill = PatternFill(start_color="FFB6C1", end_color="FFB6C1", fill_type="solid")

            # Add evidence links
            evidence_links = product.get('infringement_evidence_links',
                                         product.get('evidence_links', [product.get('link', '')]))
            links_text = "; ".join(link for link in evidence_links if link) if evidence_links else "N/A"
            evidence_cell = ws.cell(row=current_row, column=4, value=links_text)
            evidence_cell.border = thin_border
            evidence_cell.alignment = Alignment(wrap_text=True)

            current_row += 1

        current_row += 1

        # --- Executive Summary ---
        ws[f'A{current_row}'] = "Executive Summary"
        set_cell_style(ws[f'A{current_row}'], bold=True, font_size=12)
        current_row += 1

        # Generate executive summary based on risk assessments
        high_risk_products = [p for p in products_data if p.get('eou_probability', '').lower() == 'high'
                              or p.get('EoU probability', '').lower() == 'high']
        medium_risk_products = [p for p in products_data if p.get('eou_probability', '').lower() == 'medium'
                                or p.get('EoU probability', '').lower() == 'medium']

        if high_risk_products:
            high_products_text = ", ".join([f"{p.get('company', 'Unknown')}'s {p.get('model', 'Unknown')}"
                                            for p in high_risk_products[:3]])
            exec_summary = f"Based on our analysis, the products such as {high_products_text} can be converted into full EoUs. However, a thorough manual analysis is required to confirm the infringement."
        elif medium_risk_products:
            medium_products_text = ", ".join([f"{p.get('company', 'Unknown')}'s {p.get('model', 'Unknown')}"
                                              for p in medium_risk_products[:3]])
            exec_summary = f"Based on our analysis, products such as {medium_products_text} show potential for infringement but require further investigation. A thorough manual analysis is recommended to confirm potential infringement."
        else:
            exec_summary = "Based on our analysis, further investigation is required to identify products with high infringement probability. A thorough manual analysis is recommended to confirm potential infringement."

        ws[f'A{current_row}'] = exec_summary
        ws[f'A{current_row}'].alignment = Alignment(wrap_text=True)
        ws.merge_cells(f'A{current_row}:G{current_row + 2}')
        current_row += 4

        # --- Disclaimer ---
        ws[f'A{current_row}'] = "Disclaimer"
        set_cell_style(ws[f'A{current_row}'], bold=True, font_size=12)
        current_row += 1

        disclaimer = ("The information contained herein has been obtained majorly from Automated AI tools. "
                      "Wissen Research disclaims all warranties as to the accuracy, completeness or adequacy "
                      "of such information. Wissen Research shall have no liability for errors, omissions or "
                      "inadequacies in the information contained herein or for interpretations thereof.")
        ws[f'A{current_row}'] = disclaimer
        ws[f'A{current_row}'].alignment = Alignment(wrap_text=True)
        ws.merge_cells(f'A{current_row}:G{current_row + 2}')

        # --- Set Column Widths ---
        ws.column_dimensions['A'].width = 35
        ws.column_dimensions['B'].width = 45
        ws.column_dimensions['C'].width = 20
        ws.column_dimensions['D'].width = 30
        ws.column_dimensions['E'].width = 20
        ws.column_dimensions['F'].width = 20
        ws.column_dimensions['G'].width = 20

        # --- Save File ---
        output_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'generated_reports')
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        full_path = os.path.join(output_dir, output_file)
        wb.save(full_path)

        return f"Enhanced monetization report created successfully: {full_path}"

    except Exception as e:
        logger.error(f"Error creating monetization report: {str(e)}")
        return f"Error creating monetization report: {str(e)}"


# Create the FunctionTool for Google ADK
monetization_report_tool = FunctionTool(create_monetization_report_excel)