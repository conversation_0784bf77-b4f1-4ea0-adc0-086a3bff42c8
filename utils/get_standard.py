"""Tool to fetch standards data related to a patent with automated authentication."""

import logging
import requests
import json
import os
import time
import pyotp
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from dotenv import load_dotenv

from docx import Document
from docx.enum.text import WD_ALIGN_PARAGRAPH
from google.adk.tools import FunctionTool
from threading import Lock

load_dotenv()
logger = logging.getLogger(__name__)

class AuthManager:
    """Manages authentication and token refresh for the API."""

    def __init__(self):
        self.auth_url = "https://api.auth.wissenresearch.com/auth/login"
        self.email = os.getenv("WISSEN_EMAIL")
        self.password = os.getenv("WISSEN_PASSWORD")
        self.totp_secret = os.getenv("WISSEN_TOTP_SECRET")

        if not all([self.email, self.password, self.totp_secret]):
            raise ValueError("Missing required environment variables: WISSEN_EMAIL, WISSEN_PASSWORD, WISSEN_TOTP_SECRET")

        self.access_token = None
        self.refresh_token = None
        self.token_expires_at = None
        self.user_data = None
        self._lock = Lock()

        logger.info("AuthManager initialized")

    def _generate_totp(self) -> str:
        """Generate TOTP token using the secret key."""
        totp = pyotp.TOTP(self.totp_secret)
        return totp.now()

    def _login(self, totp_token: Optional[str] = None) -> Tuple[bool, str, Optional[Dict]]:
        """
        Authenticate user with email/password and optional TOTP token.
        Returns: (success, message, user_data)
        """
        headers = {
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0"
        }

        payload = {"email": self.email, "password": self.password}
        if totp_token:
            payload["totp_token"] = totp_token

        try:
            response = requests.post(self.auth_url, headers=headers, json=payload, timeout=30)
            logger.info(f"Login API response code: {response.status_code}")
            data = response.json()

            if response.status_code == 200:
                # Extract tokens from response
                self.access_token = data.get("access_token") or data.get("token")
                self.refresh_token = data.get("refresh_token")
                self.user_data = data

                # Set token expiration (default to 1 hour if not provided)
                expires_in = data.get("expires_in", 3600)  # seconds
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - 300)  # 5 min buffer

                logger.info("Login successful, tokens obtained")
                return True, "Login successful", data

            elif response.status_code == 400 and "TOTP" in data.get("error", ""):
                return False, "OTP required", None
            else:
                return False, data.get("message", "Authentication failed"), None

        except requests.exceptions.RequestException as e:
            logger.error(f"Login request error: {str(e)}")
            return False, f"Login error: {str(e)}", None
        except Exception as e:
            logger.error(f"Unexpected login error: {str(e)}")
            return False, f"Login error: {str(e)}", None

    def _refresh_access_token(self) -> bool:
        """Refresh the access token using refresh token."""
        if not self.refresh_token:
            logger.warning("No refresh token available")
            return False

        headers = {
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.refresh_token}"
        }

        # Adjust URL based on your API's refresh endpoint
        refresh_url = self.auth_url.replace("/login", "/refresh")

        try:
            response = requests.post(refresh_url, headers=headers, timeout=30)
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token") or data.get("token")

                expires_in = data.get("expires_in", 3600)
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - 300)

                logger.info("Token refreshed successfully")
                return True
            else:
                logger.warning(f"Token refresh failed: {response.status_code}")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"Token refresh error: {str(e)}")
            return False

    def _is_token_expired(self) -> bool:
        """Check if the current token is expired or about to expire."""
        if not self.access_token or not self.token_expires_at:
            return True
        return datetime.now() >= self.token_expires_at

    def get_valid_token(self) -> Optional[str]:
        """Get a valid access token, refreshing or re-authenticating if necessary."""
        with self._lock:
            # If token is valid, return it
            if not self._is_token_expired():
                return self.access_token

            logger.info("Token expired or missing, attempting to refresh/re-authenticate")

            # Try to refresh token first
            if self.refresh_token and self._refresh_access_token():
                return self.access_token

            # If refresh failed, perform full login
            logger.info("Refresh failed, performing full login")

            # First try login without TOTP
            success, message, user_data = self._login()

            if success:
                return self.access_token
            elif "OTP required" in message:
                # Generate TOTP and retry
                totp_token = self._generate_totp()
                logger.info("OTP required, generating TOTP token")

                success, message, user_data = self._login(totp_token)
                if success:
                    return self.access_token
                else:
                    logger.error(f"Login with TOTP failed: {message}")
                    return None
            else:
                logger.error(f"Initial login failed: {message}")
                return None

# Global auth manager instance
auth_manager = None

def get_auth_manager():
    """Get or create the global auth manager instance."""
    global auth_manager
    if auth_manager is None:
        auth_manager = AuthManager()
    return auth_manager

def get_standard_data(patent_number: str) -> Dict[str, Any]:
    """
    Fetches standards data related to a patent by making two API calls:
    1. Search for standards related to the patent
    2. Retrieve the content of the identified standard

    Args:
        patent_number (str): The patent number to search for related standards

    Returns:
        dict: A dictionary containing the standards data or error information
    """
    if not patent_number:
        return {"error_message": "Missing 'patent_number' argument"}

    try:
        # Get authenticated token
        auth_mgr = get_auth_manager()
        token = auth_mgr.get_valid_token()

        if not token:
            return {"error_message": "Failed to authenticate with the API"}

        headers = {
            "Authorization": f"Bearer {token}",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

        # Step 1: Search for standards related to the patent
        search_url = f"https://api.sep.wissenresearch.com/rag/search?patent_number={patent_number}"

        search_response = requests.get(search_url, headers=headers, timeout=30)
        search_response.raise_for_status()
        search_data = search_response.json()

        # Check if standards were found
        if not search_data.get("content"):
            return {"standard_found": False, "message": f"No standards found related to patent {patent_number}"}

        # Step 2: Get the first standard's details and fetch content
        standard = search_data["content"][0]
        database = standard.get("database")
        file_name = standard.get("file_name")
        file_path = standard.get("file_path")
        parent = standard.get("parent")

        content_url = "http://test.api.sep.wissenresearch.com:6051/api/get_data"
        params = {
            "source": database,
            "parent": parent,
            "file_name": file_name,
            "file_path": file_path
        }

        content_response = requests.get(content_url, headers=headers, params=params, timeout=30)
        content_response.raise_for_status()

        result = content_response.json()

        # Add metadata about the search
        result["search_metadata"] = {
            "patent_number": patent_number,
            "standards_found": len(search_data.get("content", [])),
            "retrieved_standard": {
                "database": database,
                "file_name": file_name,
                "parent": parent
            }
        }

        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed for patent {patent_number}: {e}")
        return {"error_message": f"Request error: {str(e)}"}
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON response for patent {patent_number}: {e}")
        return {"error_message": "Invalid JSON response from API"}
    except Exception as e:
        logger.error(f"Unexpected error for patent {patent_number}: {e}")
        return {"error_message": f"Unexpected error: {str(e)}"}

# Create the FunctionTool
get_standard_tool = FunctionTool(get_standard_data)

# Optional: Function to manually test authentication
def test_authentication():
    """Test function to verify authentication is working."""
    try:
        auth_mgr = get_auth_manager()
        token = auth_mgr.get_valid_token()
        if token:
            print(f"Authentication successful! Token: {token[:20]}...")
            return True
        else:
            print("Authentication failed!")
            return False
    except Exception as e:
        print(f"Authentication test error: {e}")
        return False


def create_enhanced_docx_report(
        input_text: str,
        patent_number: Optional[str] = None,
        output_file: str = 'enhanced_claim_chart_analysis_report.docx'
) -> str:
    """Convert claim chart JSON to a comprehensive DOCX report with both product and standards analysis.

    Args:
        input_text: The claim chart JSON to convert to DOCX
        patent_number: Patent number for standards retrieval
        output_file: Filename for the output DOCX file

    Returns:
        A message indicating success or failure
    """
    try:
        # Parse the input JSON
        products = json.loads(input_text)

        # Try to get standards data if patent number provided
        standards_data = None
        if patent_number:
            print(f"Retrieving standards data for patent: {patent_number}")
            standards_response = get_standard_data(patent_number)
            if not standards_response.get("error_message") and not standards_response.get("message"):
                standards_data = standards_response

        # Create a new Document
        doc = Document()

        # Add title
        title = doc.add_heading('Comprehensive Patent Analysis Report', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # Add summary section
        doc.add_heading('Executive Summary', level=1)
        summary_text = f'This report provides a comprehensive analysis including:\n'
        summary_text += f'• Product-based claim chart analysis for {len(products)} product(s)\n'
        if standards_data:
            summary_text += f'• Standards-based evidence of use analysis\n'
        summary_text += f'• Detailed infringement EoU Probability'
        doc.add_paragraph(summary_text)

        # Standards Analysis Section (if available)
        if standards_data:
            doc.add_page_break()
            doc.add_heading('Standards-Based Evidence of Use Analysis', level=1)

            # Add standards metadata
            if standards_data.get('title'):
                doc.add_heading(f"Standard: {standards_data['title']}", level=2)

            if standards_data.get('assignee'):
                doc.add_paragraph(f"Assignee: {standards_data['assignee']}")

            if standards_data.get('publication_date'):
                doc.add_paragraph(f"Publication Date: {standards_data['publication_date']}")

            # Standards claim analysis (similar to Evidence of Use Report format)
            doc.add_heading('Standards Claim Enablement Analysis', level=3)

            # Create a table for standards analysis
            table = doc.add_table(rows=1, cols=3)
            table.style = 'Table Grid'
            hdr_cells = table.rows[0].cells
            hdr_cells[0].text = 'Claim Element'
            hdr_cells[1].text = 'Standards Enablement'
            hdr_cells[2].text = 'Relevant Excerpt'

            # Make header bold
            for cell in hdr_cells:
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.font.bold = True

            # Add standards content analysis (placeholder - would need actual claim mapping)
            if standards_data.get('content'):
                doc.add_paragraph("Standards Content Available for Analysis:")
                content_para = doc.add_paragraph(str(standards_data['content'])[:1000] + "..." if len(
                    str(standards_data['content'])) > 1000 else str(standards_data['content']))
                content_para.style = 'Quote'

        # Product Analysis Section
        if products:
            doc.add_page_break()
            doc.add_heading('Product-Based Claim Chart Analysis', level=1)

            # Products overview section
            doc.add_heading('Products Overview', level=2)
            for i, product in enumerate(products, 1):
                product_header = f"{product['company']} - {product['model']}"
                doc.add_heading(product_header, level=3)
                doc.add_paragraph(f"Launch Date: {product.get('launch_date', 'N/A')}")
                doc.add_paragraph(f"Collaboration Type: {product.get('collaboration_type', 'N/A')}")
                doc.add_paragraph(f"EoU Probability: {product.get('eou_probability', 'N/A')}")

                if product.get('infringement_evidence_links'):
                    doc.add_paragraph("Evidence Links:")
                    for link in product['infringement_evidence_links']:
                        p = doc.add_paragraph(style='List Bullet')
                        p.add_run(link)

            # Detailed product analysis
            doc.add_page_break()
            doc.add_heading('Detailed Product Analysis', level=2)

            for product in products:
                product_title = f"{product['company']} - {product['model']}"
                doc.add_heading(product_title, level=3)

                # Product metadata
                doc.add_paragraph(f"Category: {product.get('category', 'N/A')}")
                doc.add_paragraph(f"Activity Date: {product.get('activity_date', 'N/A')}")
                doc.add_paragraph(f"EoU Probability: {product.get('eou_probability', 'N/A')}")

                # Risk justification
                if product.get('risk_justification'):
                    doc.add_paragraph("Risk Justification:")
                    risk_para = doc.add_paragraph(product['risk_justification'])
                    risk_para.style = 'Intense Quote'

                # Claim Chart section
                doc.add_heading('Claim Chart Analysis', level=4)

                if product.get('claim_chart'):
                    for claim_item in product['claim_chart']:
                        claim_para = doc.add_paragraph()
                        claim_run = claim_para.add_run(claim_item.get('claim_element', ''))
                        claim_run.bold = True

                        feature_para = doc.add_paragraph(
                            f"Corresponding Feature: {claim_item.get('corresponding_feature', 'N/A')}")
                        source_para = doc.add_paragraph(
                            f"Source Justification: {claim_item.get('source_justification', 'N/A')}")
                        source_para.style = 'Quote'
                        doc.add_paragraph("")

                # Sources section
                if product.get('sources'):
                    doc.add_heading('Sources', level=4)
                    for source in product['sources']:
                        doc.add_paragraph(source, style='List Bullet')

                if product != products[-1]:
                    doc.add_page_break()

        # Add disclaimer and limitations
        doc.add_page_break()
        doc.add_heading('Disclaimer and Limitations', level=1)

        disclaimer_text = """
        This analysis has been conducted based on available public information and technical standards. 
        The information contained herein is believed to be reliable, but no warranties are made as to 
        accuracy, completeness, or adequacy. This report does not constitute legal advice and should 
        be reviewed by qualified legal counsel for any legal determinations.

        Limitations:
        • Analysis based on publicly available official sources only
        • Standards analysis dependent on available technical documentation
        • Product analysis limited to official company disclosures
        • Independent legal analysis recommended for litigation purposes
        """
        doc.add_paragraph(disclaimer_text)

        # Save the document
        doc.save(output_file)
        return f"Enhanced DOCX report created successfully: {output_file}"

    except json.JSONDecodeError as e:
        return f"Error parsing JSON input: {str(e)}"
    except Exception as e:
        return f"Error creating enhanced DOCX report: {str(e)}"

enhanced_docx_report_tool = FunctionTool(create_enhanced_docx_report)

if __name__ == "__main__":
    # Test the authentication system
    test_authentication()

    # Test fetching standard data
    result = get_standard_data("US10741843B2")
    print(json.dumps(result, indent=2))