import json
import pandas as pd
import re
from typing import List, Dict, Any, Optional

from google.adk.tools import FunctionTool
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter


def parse_claim_chart_text(input_text: str) -> List[Dict[str, Any]]:
    """
    Parse the text-based claim chart analysis into a structured dictionary format.
    This handles the text format shown in the sample output.

    Args:
        input_text: The claim chart text to parse

    Returns:
        List of product dictionaries with claim chart data
    """
    products = []
    current_product = None
    claim_chart_items = []

    # Match product header and extract key information
    product_pattern = re.compile(
        r"Product \d+: (.+)\n\nCompany: (.+)\nModel: (.+)\nCategory: (.+)\nActivity Date.*?: (.+)\nEoU Probability: (.+)\nRisk Justification: (.+)")

    # Match claim element entries
    claim_element_pattern = re.compile(
        r"Claim \d+, Element [A-Z]: (.+)\nCorresponding Feature: (.+)\nSource Justification: \"(.+)\"")

    # Match sources line
    sources_pattern = re.compile(r"Sources:\n(.+)")

    lines = input_text.split('\n')
    i = 0

    while i < len(lines):
        line = lines[i]

        # Check for a new product section
        if line.startswith("Product "):
            # Save previous product if exists
            if current_product:
                current_product["claim_chart"] = claim_chart_items
                products.append(current_product)
                claim_chart_items = []

            # Find the end of the product metadata block
            product_text = ""
            j = i
            while j < len(lines) and not lines[j].startswith("Claim Chart Snippets"):
                product_text += lines[j] + "\n"
                j = j + 1

            # Extract product information
            match = product_pattern.search(product_text)
            if match:
                product_name, company, model, category, activity_date, eou_probability, risk_justification = match.groups()

                # Extract sources
                sources_match = sources_pattern.search(product_text)
                sources = []
                if sources_match:
                    sources = [s.strip() for s in sources_match.group(1).split('\n')]

                current_product = {
                    "company": company,
                    "model": model,
                    "category": category,
                    "activity_date": activity_date,
                    "eou_probability": eou_probability,
                    "risk_justification": risk_justification,
                    "sources": sources,
                    "search_queries": []  # Not provided in sample, but included in structure
                }

            i = j  # Move to claim chart section

        # Process claim chart entries
        elif "Claim " in line and ", Element " in line and i + 2 < len(lines):
            claim_element = line
            corresponding_feature = lines[i + 1]
            source_justification = lines[i + 2]

            if corresponding_feature.startswith("Corresponding Feature: "):
                corresponding_feature = corresponding_feature[len("Corresponding Feature: "):]

            if source_justification.startswith("Source Justification: "):
                source_justification = source_justification[len("Source Justification: "):]

            # Clean up quotes if present
            if source_justification.startswith('"') and source_justification.endswith('"'):
                source_justification = source_justification[1:-1]

            claim_chart_items.append({
                "claim_element": claim_element,
                "corresponding_feature": corresponding_feature,
                "source_justification": source_justification
            })

            i += 3  # Move past the 3 lines we just processed
        else:
            i += 1

    # Don't forget to add the last product
    if current_product:
        current_product["claim_chart"] = claim_chart_items
        products.append(current_product)

    return products


def format_excel_sheet(worksheet, header_row=1):
    """Apply formatting to an Excel worksheet"""
    # Define styles
    header_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
    header_font = Font(bold=True)
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # Apply styles to header row
    for cell in worksheet[header_row]:
        if cell.value:  # Only format cells with content
            cell.fill = header_fill
            cell.font = header_font
            cell.border = border
            cell.alignment = Alignment(wrap_text=True, vertical='center')

    # Apply borders and text wrapping to all cells with content
    for row in worksheet.iter_rows(min_row=header_row + 1):
        for cell in row:
            if cell.value:  # Only format cells with content
                cell.border = border
                cell.alignment = Alignment(wrap_text=True, vertical='top')


def create_excel_from_claim_charts(input_text: str, output_file: str = 'claim_chart_analysis.xlsx') -> str:
    """Convert claim chart JSON to an Excel file with product data, claim charts, and standards analysis.

    Args:
        input_text: The claim chart JSON to convert to Excel
        output_file: Filename for the output Excel file

    Returns:
        A message indicating success or failure
    """
    try:
        # Parse the input - expect JSON format based on the provided data
        try:
            data = json.loads(input_text)
            # Check if it's the full analysis structure or just products
            if isinstance(data, dict) and 'products_analysis' in data:
                products = data['products_analysis']
                standards_analysis = data.get('standards_analysis', {})
            else:
                products = data if isinstance(data, list) else [data]
                standards_analysis = {}
        except json.JSONDecodeError:
            # Fallback to text parsing
            products = parse_claim_chart_text(input_text)
            standards_analysis = {}

        if not products:
            return "No products found in input data."

        # Create a new Excel workbook
        wb = Workbook()

        # Create the main products sheet
        main_sheet = wb.active
        main_sheet.title = "Products Overview"

        # Set headers for the main sheet
        headers = ["Company", "Model", "Category", "Activity Date", "EoU Probability", "Risk Justification"]
        main_sheet.append(headers)

        # Add product data to the main sheet
        for product in products:
            main_sheet.append([
                product.get("company", ""),
                product.get("model", ""),
                product.get("category", ""),
                product.get("activity_date", ""),
                product.get("eou_probability", ""),
                product.get("risk_justification", "")
            ])

        # Format the main sheet
        for col in range(1, len(headers) + 1):
            column_letter = get_column_letter(col)
            main_sheet.column_dimensions[column_letter].width = 25

        format_excel_sheet(main_sheet)

        # Add standards analysis sheet if available
        if standards_analysis:
            standards_sheet = wb.create_sheet(title="Standards Analysis")

            # Add standards metadata
            standards_sheet.append(["Standards Analysis Summary"])
            standards_sheet.append([""])
            standards_sheet.append(["Standard Found:", standards_analysis.get("standard_found", "N/A")])
            standards_sheet.append(["Standard Title:", standards_analysis.get("standard_title", "N/A")])
            standards_sheet.append(["Standard Body:", standards_analysis.get("standard_body", "N/A")])
            standards_sheet.append(["Publication Date:", standards_analysis.get("publication_date", "N/A")])
            standards_sheet.append(
                ["Overall Assessment:", standards_analysis.get("overall_enablement_assessment", "N/A")])
            standards_sheet.append([""])

            # Add claim enablement analysis
            if standards_analysis.get("claim_enablement_analysis"):
                standards_sheet.append(["Claim Enablement Analysis"])
                standards_sheet.append(
                    ["Claim Element", "Standards Enablement", "Relevant Excerpt", "Section Reference"])

                for analysis in standards_analysis["claim_enablement_analysis"]:
                    standards_sheet.append([
                        analysis.get("claim_element", ""),
                        analysis.get("standards_enablement", ""),
                        analysis.get("relevant_excerpt", ""),
                        analysis.get("section_reference", "")
                    ])

            # Format standards sheet
            format_excel_sheet(standards_sheet, header_row=9)  # Adjust based on where the table starts

            # Set column widths for standards sheet
            standards_sheet.column_dimensions['A'].width = 50
            standards_sheet.column_dimensions['B'].width = 20
            standards_sheet.column_dimensions['C'].width = 60
            standards_sheet.column_dimensions['D'].width = 40

        # Create individual sheets for each product
        for i, product in enumerate(products, 1):
            # Create a safe sheet name (Excel limits sheet names to 31 chars and certain characters are invalid)
            company = product.get("company", f"Product{i}")
            model = product.get("model", "")

            sheet_name = f"{company} - {model}" if model else company
            if len(sheet_name) > 31:
                sheet_name = sheet_name[:28] + "..."

            # Remove invalid characters for Excel sheet names
            invalid_chars = ['[', ']', '*', '?', ':', '/', '\\']
            for char in invalid_chars:
                sheet_name = sheet_name.replace(char, '_')

            # Handle duplicate sheet names
            original_name = sheet_name
            suffix = 1
            while sheet_name in wb.sheetnames:
                sheet_name = f"{original_name[:27]}_{suffix}"
                suffix += 1

            # Create the product sheet
            product_sheet = wb.create_sheet(title=sheet_name)

            current_row = 1

            # Add product metadata
            product_sheet.cell(row=current_row, column=1, value="Product Information")
            product_sheet.cell(row=current_row, column=1).font = Font(bold=True, size=14)
            current_row += 1

            product_sheet.cell(row=current_row, column=1, value="Company:")
            product_sheet.cell(row=current_row, column=2, value=product.get("company", ""))
            current_row += 1

            product_sheet.cell(row=current_row, column=1, value="Model:")
            product_sheet.cell(row=current_row, column=2, value=product.get("model", ""))
            current_row += 1

            product_sheet.cell(row=current_row, column=1, value="Category:")
            product_sheet.cell(row=current_row, column=2, value=product.get("category", ""))
            current_row += 1

            product_sheet.cell(row=current_row, column=1, value="Activity Date:")
            product_sheet.cell(row=current_row, column=2, value=product.get("activity_date", ""))
            current_row += 1

            product_sheet.cell(row=current_row, column=1, value="EoU Probability:")
            product_sheet.cell(row=current_row, column=2, value=product.get("eou_probability", ""))
            current_row += 1

            product_sheet.cell(row=current_row, column=1, value="Risk Justification:")
            product_sheet.cell(row=current_row, column=2, value=product.get("risk_justification", ""))
            current_row += 2

            # Add sources
            if product.get("sources"):
                product_sheet.cell(row=current_row, column=1, value="Sources:")
                product_sheet.cell(row=current_row, column=1).font = Font(bold=True)
                current_row += 1
                for source in product["sources"]:
                    product_sheet.cell(row=current_row, column=1, value=source)
                    current_row += 1
                current_row += 1

            # Add search queries if available
            if product.get("search_queries"):
                product_sheet.cell(row=current_row, column=1, value="Search Queries:")
                product_sheet.cell(row=current_row, column=1).font = Font(bold=True)
                current_row += 1
                for query in product["search_queries"]:
                    product_sheet.cell(row=current_row, column=1, value=query)
                    current_row += 1
                current_row += 1

            # Add claim chart
            product_sheet.cell(row=current_row, column=1, value="Claim Chart Analysis")
            product_sheet.cell(row=current_row, column=1).font = Font(bold=True, size=14)
            current_row += 1

            claim_chart_header_row = current_row
            product_sheet.cell(row=current_row, column=1, value="Claim Element")
            product_sheet.cell(row=current_row, column=2, value="Corresponding Feature")
            product_sheet.cell(row=current_row, column=3, value="Source Justification")
            current_row += 1

            # Add claim chart data
            if product.get("claim_chart"):
                for item in product["claim_chart"]:
                    product_sheet.cell(row=current_row, column=1, value=item.get("claim_element", ""))
                    product_sheet.cell(row=current_row, column=2, value=item.get("corresponding_feature", ""))
                    product_sheet.cell(row=current_row, column=3, value=item.get("source_justification", ""))
                    current_row += 1

            current_row += 1

            # Format the claim chart table
            format_excel_sheet(product_sheet, header_row=claim_chart_header_row)

            # Adjust column widths
            product_sheet.column_dimensions['A'].width = 45
            product_sheet.column_dimensions['B'].width = 45
            product_sheet.column_dimensions['C'].width = 65
            product_sheet.column_dimensions['D'].width = 40

        # Save the workbook
        wb.save(output_file)
        return f"Excel file created successfully: {output_file}"

    except Exception as e:
        return f"Error creating Excel file: {str(e)}"


# Create the FunctionTool
claim_chart_tool = FunctionTool(create_excel_from_claim_charts)