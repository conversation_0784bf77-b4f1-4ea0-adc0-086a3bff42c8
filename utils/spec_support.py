import json
import logging
from typing import Dict, List
import asyncio
import aiohttp
from google.adk.tools import FunctionTool

logger = logging.getLogger(__name__)

BASE_URL = "http://135.181.19.83:8531"
REQUEST_TIMEOUT = 300


async def fetch_keyfeatures(session: aiohttp.ClientSession, claim: str) -> List[str]:
    """Helper to fetch keyfeatures for a single claim."""
    claim_data = {"independent_claim": claim}
    try:
        async with session.post(f"{BASE_URL}/keyfeature_breakage", json=claim_data) as response:
            if response.status != 200:
                logger.error(f"Keyfeature breakage error for claim: {await response.text()}")
                return []
            result = await response.json()
            return result.get('keyfeature_list', [])
    except Exception as e:
        logger.error(f"Exception in fetch_keyfeatures: {e}")
        return []


async def fetch_keyconcepts(session: aiohttp.ClientSession, kf_list: List[str]) -> List:
    """Fetch keyconcepts for keyfeature list."""
    data = {"keyfeature_list": kf_list}
    try:
        async with session.post(f"{BASE_URL}/keyconcepts", json=data) as response:
            if response.status != 200:
                logger.error(f"Keyconcepts error: {await response.text()}")
                return []
            resp_json = await response.json()
            if 'keyconcepts' not in resp_json:
                logger.error(f"Missing 'keyconcepts' in response: {resp_json}")
                return []
            return resp_json['keyconcepts']
    except Exception as e:
        logger.error(f"Exception in fetch_keyconcepts: {e}")
        return []


async def fetch_spec_support(session: aiohttp.ClientSession, kf_list: List[str], description: str) -> List:
    """Fetch spec support for keyfeature list."""
    data = {
        "keyfeature": kf_list,
        "description": description
    }
    try:
        async with session.post(f"{BASE_URL}/spec_support", json=data) as response:
            if response.status != 200:
                logger.error(f"Spec support error: {await response.text()}")
                return []
            result = await response.json()
            return result.get('data', [])
    except Exception as e:
        logger.error(f"Exception in fetch_spec_support: {e}")
        return []


async def get_spec_support(patent_number: str) -> Dict:
    """
    Fetches keyfeature breakage and specification support for a patent by patent number.

    Args:
        patent_number (str): The patent number to analyze.

    Returns:
        dict: A dictionary containing keyfeatures_list and spec_support data or error message if request fails.
    """
    async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=REQUEST_TIMEOUT),
            connector=aiohttp.TCPConnector(limit=50, limit_per_host=20)
    ) as session:

        # Fetch claims data
        params = {"patent_number": patent_number}
        try:
            async with session.post(f"{BASE_URL}/select_claims", params=params) as response:
                if response.status != 200:
                    error_text = await response.text()
                    return {"error": f"Failed to fetch claims: {error_text}"}

                data = await response.json()
                description = data.get('description', '')
                independent_claims = data.get('independent_claim_list', [])

        except Exception as e:
            return {"error": f"Failed to fetch claims: {str(e)}"}

        if not independent_claims:
            return {
                "keyfeatures_list": [],
                "keyconcepts": [],
                "spec_support": []
            }

        # Parallel keyfeature breakage for all claims
        keyfeatures_tasks = [fetch_keyfeatures(session, claim) for claim in independent_claims]
        keyfeatures_nested = await asyncio.gather(*keyfeatures_tasks)
        keyfeatures_list = [kf for sublist in keyfeatures_nested for kf in sublist]

        # Parallel keyconcepts and spec_support processing
        keyconcepts_task = fetch_keyconcepts(session, keyfeatures_list)
        spec_support_task = fetch_spec_support(session, keyfeatures_list, description)

        keyconcepts, spec_support = await asyncio.gather(keyconcepts_task, spec_support_task)

        return {
            "keyfeatures_list": keyfeatures_list,
            "keyconcepts": keyconcepts,
            "spec_support": spec_support
        }


get_spec_support_tool = FunctionTool(get_spec_support)

if __name__ == "__main__":
    result = asyncio.run(get_spec_support("EP3915337B1"))
    print(json.dumps(result, indent=2))