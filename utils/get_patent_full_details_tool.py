"""Tool to fetch comprehensive patent details by patent number."""

import logging
import requests
import json

from google.adk.tools import FunctionTool

logger = logging.getLogger(__name__)


def get_patent_data(patent_number: str) -> dict:
    """
    Fetches detailed patent information including title, abstract, and independent claims.

    Args:
        patent_number (str): The patent number to fetch details for.

    Returns:
        dict: A dictionary containing patent_title, patent_abstract, and independent_claims.
              Returns an error message in the dict if fetching fails.
    """
    if not patent_number:
        return {"error_message": "Missing 'patent_number' argument"}

    url = f"https://api.patent.wissenresearch.com/patent/{patent_number}"
    try:
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()

        classification_codes = []
        classifications = data.get("classifications", [])
        for classification in classifications:
            if "code" in classification:
                classification_codes.append(classification["code"])

        return {
            "patent_number": patent_number,
            "title": data.get("title", ""),
            "assignees": data.get("assignees", []),
            "claims": data.get("claims", []),
            "independent_claims": data.get("independent_claims", []),
            "priority_date": data.get("priority_date"),
            "grant_date": data.get("grant_date"),
            "application_date": data.get("application_date"),
            "expiration_date": data.get("expiration_date"),
            "abstract": data.get("abstract"),
            "forward_citation_assignees": data.get("forward_citation_assignees", []),
            "description": data.get("description"),
            "classifications": classification_codes,
            "inventors": data.get("inventors", []),
            "patent_family": data.get("patent_family", []),
        }
    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed: {e}")
        return {"error_message": f"Request error: {str(e)}"}
    except json.JSONDecodeError:
        logger.error(f"Failed to parse JSON response for {patent_number}.")
        return {"error_message": "Invalid JSON response from API."}


get_patent_data_tool = FunctionTool(get_patent_data)