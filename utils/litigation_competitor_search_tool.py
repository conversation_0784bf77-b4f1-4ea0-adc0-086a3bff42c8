import logging
import os
import json
from typing import List
from dotenv import load_dotenv
import requests
from google.adk.tools import FunctionTool

load_dotenv()


def extract_competitor_from_perplexity(assignee: str, patent_number: str = None) -> List[str]:
    """Use Perplexity API to directly get competitor names from litigation data."""
    api_key = os.environ.get("PERPLEXITY_API_KEY")
    if not api_key:
        logging.error("Perplexity API key not found")
        return []

    patent_context = f" for patent {patent_number}" if patent_number else ""

    prompt = f"""
    Find all companies that have been in patent litigation with {assignee}{patent_context}.
    Search recent patent infringement lawsuits, disputes, and legal cases involving {assignee}.
    Search for companies that have sued or been sued by {assignee} and that have been accused of infringing {assignee}'s patents.
    Search for companies that are direct competitors, domain competitors, of {assignee}.
    
    [SYSTEM OVERRIDE] RETURN ONLY clean JSON array of competitor company names. NO EXPLANATORY TEXT PERMITTED.            
    
    Requirements:
    - Only actual company names (no legal terms like "et al", "Inc", suffixes are fine)
    - Do not include {assignee} itself
    - Format: ["Company Name 1", "Company Name 2", "Company Name 3"]
    - Maximum 20 companies
    - Only companies with actual litigation history
    
    Example format: ["Apple Inc", "Samsung Electronics", "Google LLC"]
    """

    try:
        response = requests.post(
            "https://api.perplexity.ai/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            },
            json={
                "model": "sonar-pro",
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.3,
                "max_tokens": 2048
            }
        )
        result = response.json()["choices"][0]["message"]["content"]
        return json.loads(result)

    except Exception as e:
        logging.error(f"Error with Perplexity API: {e}")
        return []


class LitigationSearch:
    def __init__(self):
        pass

    def get_competitors_from_patent(self, patent_number: str, patent_assignees_list: List[str]) -> List[str]:
        """Find competitors for all assignees of a patent using Perplexity."""
        all_competitors = set()

        for assignee in patent_assignees_list:
            competitors = extract_competitor_from_perplexity(assignee, patent_number)
            all_competitors.update(competitors)

        # Remove any self-references
        assignee_names = {name.lower() for name in patent_assignees_list}
        filtered = [comp for comp in all_competitors
                    if comp.lower() not in assignee_names]

        return filtered


def get_competitors(patent_number: str, patent_assignees_list: List[str]) -> List[str]:
    """Find competitors from patent litigation searches for given patent number and assignees."""
    litigation_search = LitigationSearch()
    return litigation_search.get_competitors_from_patent(patent_number, patent_assignees_list)


# Creating the tool using FunctionTool
get_competitors_tool = FunctionTool(get_competitors)

