from typing import List, Optional
from pydantic import BaseModel


class PatentRequest(BaseModel):
    patent_number: str

class KeyConceptItem(BaseModel):
    keyfeature: str
    keyconcept: str

class SpecSupportItem(BaseModel):
    key_feature: str
    relevant_specification_1: str
    relevant_specification_2: Optional[str] = None
    relevant_specification_3: Optional[str] = None

class InfringementRequest(BaseModel):
    patent_number: str
    priority_date: str
    assignees: List[str]
    grant_date: str
    application_date: str
    expiration_date: str
    abstract: str
    classifications: List[str]
    inventors: List[str]
    patent_family: List[str]
    competitors: List[str]
    keyconcepts: List[KeyConceptItem]
    keyfeature_list: List[str]
    spec_support: List[SpecSupportItem]
    forward_citation_assignees: List[str]
    novelty_summary: str

class InfringedModel(BaseModel):
    patent_number: str
    assignees: List[str]
    priority_date: str
    novelty_summary: str
    keyfeature_list: List[str]
    company: str
    model: str
    launch_date: str
    competitor_type: str
    infringement_evidence_links: List[str]
    spec_support: List[SpecSupportItem]

class ReverseEngineeringRequest(BaseModel):
    infringed_models: List[InfringedModel]

    class Config:
        extra = "allow"

    @property
    def patent_number(self) -> Optional[str]:
        """Get the patent number from the first infringed model"""
        if self.infringed_models:
            return self.infringed_models[0].patent_number
        return None

class ClaimChartRequest(BaseModel):
    infringed_models: List[InfringedModel]

    class Config:
        extra = "allow"

    @property
    def patent_number(self) -> Optional[str]:
        """Get the patent number from the first infringed model"""
        if self.infringed_models:
            return self.infringed_models[0].patent_number
        return None
