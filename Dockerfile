FROM python:3.13-slim
WORKDIR /app

# Copy and install requirements first (for better caching)
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Create user and set up directories
RUN adduser --disabled-password --gecos "" myuser

# Copy application files
COPY . .

# Create data directory for databases and set permissions
RUN mkdir -p /app/data && \
    chown -R myuser:myuser /app && \
    chmod -R 755 /app && \
    chmod -R 775 /app/data

# Switch to non-root user
USER myuser
ENV PATH="/home/<USER>/.local/bin:$PATH"

EXPOSE 8001
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8001"]