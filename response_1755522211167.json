{"patent_number": "EP3915337B1", "title": "Dynamic user interface", "abstract": "Abstract not found.", "assignees": ["Signify Holding BV"], "inventors": ["<PERSON><PERSON> VAN DER SLOOT", "Rob <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> VRIES"], "application_date": "2020-01-17", "priority_date": "2019-01-21", "grant_date": "2023-11-15", "expiration_date": "2040-01-17", "patent_family": ["ES2969610T3", "EP3915337B1", "CN113287371B", "WO2020152058A1", "US11490493B2"], "classifications": [], "forward_citation_assignees": ["Shopify Inc.", "ヤマハ株式会社"], "competitors": ["EEMA Industries", "ETI Solid State Lighting", "<PERSON>", "<PERSON><PERSON>", "Seoul Semiconductor", "Luminex International"], "keyconcepts": [{"keyfeature": "A user interface (100) for controlling a multichannel lighting unit (530) wherein each channel (B, G, R, FR) of the multichannel lighting unit comprises at least one light source (540) for emitting light having a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the user interface comprising:- at least two user interaction elements (10, 20), each user interaction element associated with a color producible with the multichannel lighting unit, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising:- a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit,- a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element,- a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element,", "keyconcept": "A user interface (100) for controlling a multichannel lighting unit (530) where each channel (B, G, R, FR) has at least one light source (540) emitting light with a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), where spectral compositions differ between channels and light output from each channel is individually controllable. The user interface includes at least two user interaction elements (10, 20), each associated with a producible color, and at least one of these colors does not have a directly corresponding channel. Each user interaction element comprises: a static scale (11, 21) representing a static range of control values for controlling light output based on the associated color, with the range determined by the minimum and maximum light output in that color; a dynamic scale (12, 22) representing a dynamic range of control values for controlling light output based on the associated color; and a selector (13, 23) adjustable by a user to select a present control value for the light output based on the associated color. The dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale."}, {"keyfeature": "characterized in thatthe dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, andthe dynamic scale is depicted as a subrange of the static scale and overlaying said static scale.", "keyconcept": "The dynamic range of control values for a user interaction element is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, specifically within the context of controlling a multichannel lighting unit where each user interaction element is associated with a color producible by the unit."}, {"keyfeature": "A method of controlling a multichannel lighting unit (530) wherein each channel (B, G, R, FR) of the multichannel lighting unit comprises at least one light source (540) for emitting light having a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising:-", "keyconcept": "A method of controlling a multichannel lighting unit (530) via a user interface (100) with at least two user interaction elements (10, 20), where each channel (B, G, R, FR) has a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR) and individually controllable light output. The method involves providing user interaction elements, each with a static scale (11, 21) and a dynamic scale (12, 22) for controlling light output in respect of an associated color, where the dynamic range is a valid/executable range determined by a present control value selected from another user interaction element, and the dynamic scale is depicted as a subrange of and overlaying the static scale."}, {"keyfeature": "providing at least two user interaction elements (10, 11), each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising:-", "keyconcept": "providing at least two user interaction elements (10, 11), each user interaction element associated with a color producible with the multichannel lighting unit, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising a static scale (11, 21), a dynamic scale (12, 22), and a selector (13, 23)"}, {"keyfeature": "a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit,-", "keyconcept": "a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit (530) in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, and where the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit"}, {"keyfeature": "a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element,-", "keyconcept": "a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale"}, {"keyfeature": "a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element;-", "keyconcept": "a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element, where the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale."}, {"keyfeature": "selecting, using the selector of a first of the at least two user interaction elements, a present control value for the color associated with the first user interaction element;-", "keyconcept": "selecting, using the selector (13, 23) of a first of the at least two user interaction elements (10, 20), a present control value for the color associated with the first user interaction element, where the color is producible with the multichannel lighting unit (530) and may not have a directly corresponding channel."}, {"keyfeature": "adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and-", "keyconcept": "adapting the dynamic scale of a second user interaction element, based on the present control value selected in respect of the first user interaction element, where the dynamic scale represents a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, and the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale"}, {"keyfeature": "controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element,characterized in thatthe dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, andthe dynamic scale is depicted as a subrange of the static scale and overlaying said static scale.", "keyconcept": "controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element, where the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, within a user interface for controlling a multichannel lighting unit where each channel has a channel-specific spectral composition and light output is individually controllable, and at least two user interaction elements are provided, each associated with a color producible by the lighting unit, with at least one color not having a directly corresponding channel, and each user interaction element includes a static scale representing a static range of control values (determined by minimum and maximum light output in the associated color), a dynamic scale representing a dynamic range of control values, and a selector for selecting a present control value."}], "keyfeature_list": ["A user interface (100) for controlling a multichannel lighting unit (530) wherein each channel (B, G, R, FR) of the multichannel lighting unit comprises at least one light source (540) for emitting light having a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the user interface comprising:- at least two user interaction elements (10, 20), each user interaction element associated with a color producible with the multichannel lighting unit, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising:- a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit,- a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element,- a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element,", "characterized in thatthe dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, andthe dynamic scale is depicted as a subrange of the static scale and overlaying said static scale.", "A method of controlling a multichannel lighting unit (530) wherein each channel (B, G, R, FR) of the multichannel lighting unit comprises at least one light source (540) for emitting light having a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising:-", "providing at least two user interaction elements (10, 11), each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising:-", "a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit,-", "a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element,-", "a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element;-", "selecting, using the selector of a first of the at least two user interaction elements, a present control value for the color associated with the first user interaction element;-", "adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and-", "controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element,characterized in thatthe dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, andthe dynamic scale is depicted as a subrange of the static scale and overlaying said static scale."], "spec_support": [{"key_feature": "A user interface (100) for controlling a multichannel lighting unit (530) wherein each channel (B, G, R, FR) of the multichannel lighting unit comprises at least one light source (540) for emitting light having a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the user interface comprising:- at least two user interaction elements (10, 20), each user interaction element associated with a color producible with the multichannel lighting unit, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising:- a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit,- a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element,- a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element,", "relevant_specification_1": "Described herein is a user interface for controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the user interface comprising: at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element.", "relevant_specification_2": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color.", "relevant_specification_3": "The color associated with a user interaction elements does not necessarily directly associate with or relate to a channel of the multichannel lighting unit. Examples include a green color that is not directly associated with a green channel of the multichannel lighting unit but is indirectly linked to the white channel because the white channel will also provide green light emission, or a far-red color that is not directly associated with a far-red channel of the multichannel lighting unit but is indirectly linked to the red channel which also provides some far-red light emission. Therefore, according to the invention, the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit."}, {"key_feature": "characterized in thatthe dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, andthe dynamic scale is depicted as a subrange of the static scale and overlaying said static scale.", "relevant_specification_1": "Described herein is a user interface for controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the user interface comprising: at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element.", "relevant_specification_2": "The dynamic range of a first of the at least two user interaction elements is automatically adapted when the present control value for a second of the at least two user interaction elements is changed using the selector of the second of the at least two user interaction elements. A valid/executable range of control values for a particular user interaction element is determined based on (1) technical characteristics of the multichannel lighting unit such the number and spectral composition of the different channels of the multichannel lighting unit and maximum power consumption of the lighting unit, and (2) operational characteristics of the multichannel lighting unit such as selected control values for other user interaction elements. Control values may include luminous/radiant power or luminous/radiant flux, luminous/radiant intensity, illuminance/irradiance or any other value representing an amount of light/radiation. In general, the dynamic range of a user interaction elements will be a subrange of the static range of that user interaction elements. In embodiments, the dynamic range excludes the end points of the static range, more specifically a minimum control value of the dynamic range of a user interaction element is larger than a minimum control value of the static range for that user interaction element. In embodiment, the selector may overlay the dynamic scale, which itself overlays the static scale. Some or all of the static scale, dynamic scale and selector may be semitransparent such that they maintain visible to the user or operator of the user interface even when overlaid.", "relevant_specification_3": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color."}, {"key_feature": "A method of controlling a multichannel lighting unit (530) wherein each channel (B, G, R, FR) of the multichannel lighting unit comprises at least one light source (540) for emitting light having a channel-specific spectral composition (CH1:B, CH2:W, CH3:R, CH4,FR), the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising:-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "The dynamic range of a first of the at least two user interaction elements is automatically adapted when the present control value for a second of the at least two user interaction elements is changed using the selector of the second of the at least two user interaction elements. A valid/executable range of control values for a particular user interaction element is determined based on (1) technical characteristics of the multichannel lighting unit such the number and spectral composition of the different channels of the multichannel lighting unit and maximum power consumption of the lighting unit, and (2) operational characteristics of the multichannel lighting unit such as selected control values for other user interaction elements. Control values may include luminous/radiant power or luminous/radiant flux, luminous/radiant intensity, illuminance/irradiance or any other value representing an amount of light/radiation. In general, the dynamic range of a user interaction elements will be a subrange of the static range of that user interaction elements. In embodiments, the dynamic range excludes the end points of the static range, more specifically a minimum control value of the dynamic range of a user interaction element is larger than a minimum control value of the static range for that user interaction element. In embodiment, the selector may overlay the dynamic scale, which itself overlays the static scale. Some or all of the static scale, dynamic scale and selector may be semitransparent such that they maintain visible to the user or operator of the user interface even when overlaid."}, {"key_feature": "providing at least two user interaction elements (10, 11), each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising:-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color.", "relevant_specification_3": "The color associated with a user interaction elements does not necessarily directly associate with or relate to a channel of the multichannel lighting unit. Examples include a green color that is not directly associated with a green channel of the multichannel lighting unit but is indirectly linked to the white channel because the white channel will also provide green light emission, or a far-red color that is not directly associated with a far-red channel of the multichannel lighting unit but is indirectly linked to the red channel which also provides some far-red light emission. Therefore, according to the invention, the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit."}, {"key_feature": "a static scale (11, 21) representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit,-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "The invention is based on the idea of providing a dynamic light output range taking into account the technicalities listed above, e.g. the interactions between colors and power limitations, and guide an operators, lighting designer or light protocol developer through the process in defining a valid specific mix of light colors with a specific light intensity executable with the (multichannel) lighting unit(s) of a lighting system. Such dynamic light ouput range provides a range of control values for light output of a particular color between a minimum exectuable light output and a maximum executable light output, i.a. based on the actual light output settings for other colors. The dynamic light output range may be presented in addition to an static light output range providing a range of control values for light ouptut of that same particular color between an absolute minimum and absolute maximum light output as determined by the specifications of the lighting unit or lighting system itself and not taking into account actual light output settings of other colors. Phrased differently, the static light output range may regarded as a 'design range', as it is determined by design of the lighting unit/lighting system, whereas the dynamic light output range may be regarded as an `operational range'. Per adjustment made in a light setting or light protocol, the dynamic ranges for all the light colors adjust themselves, where the static ranges are fixed and remain the same.", "relevant_specification_3": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color."}, {"key_feature": "a dynamic scale (12, 22) representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element,-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color.", "relevant_specification_3": "The invention is based on the idea of providing a dynamic light output range taking into account the technicalities listed above, e.g. the interactions between colors and power limitations, and guide an operators, lighting designer or light protocol developer through the process in defining a valid specific mix of light colors with a specific light intensity executable with the (multichannel) lighting unit(s) of a lighting system. Such dynamic light ouput range provides a range of control values for light output of a particular color between a minimum exectuable light output and a maximum executable light output, i.a. based on the actual light output settings for other colors. The dynamic light output range may be presented in addition to an static light output range providing a range of control values for light ouptut of that same particular color between an absolute minimum and absolute maximum light output as determined by the specifications of the lighting unit or lighting system itself and not taking into account actual light output settings of other colors. Phrased differently, the static light output range may regarded as a 'design range', as it is determined by design of the lighting unit/lighting system, whereas the dynamic light output range may be regarded as an `operational range'. Per adjustment made in a light setting or light protocol, the dynamic ranges for all the light colors adjust themselves, where the static ranges are fixed and remain the same."}, {"key_feature": "a selector (13, 23) adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element;-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color.", "relevant_specification_3": "In embodiment, the selector may overlay the dynamic scale, which itself overlays the static scale. Some or all of the static scale, dynamic scale and selector may be semitransparent such that they maintain visible to the user or operator of the user interface even when overlaid."}, {"key_feature": "selecting, using the selector of a first of the at least two user interaction elements, a present control value for the color associated with the first user interaction element;-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "Figures 1A and 1B each disclose 2 user interaction elements 10, 20. The user interaction element 10 is associated with the color blue, the user interaction element 20 is associated with the color green. Figures 1A and 1B could represent a user interface for controlling the light output of a multichannel lighting unit capable of emitting blue and green light. Such multichannel lighting unit may for example comprise a first channel comprising LEDs emitting in the blue wavelength range and a second channel comprising LEDs emitting white light. White LEDs typically emit light in the blue, green and red wavelength range. Each user interaction element 10, 20 includes a static scale 11, 21 (depicted over the full width of the figure) and a dynamic scale 12, 22 depicted as a subrange (not spanning the full width of the figure) overlaying the static scale 11, 21. A selector 13, 23 overlies the dynamic scale 12, 22 and the static scale 11, 21 for allowing a user or operator of the user interface to select a control value for a particular color within the static range 11, 21 of the user interaction element associated with that particular color."}, {"key_feature": "adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and-", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "The dynamic range of a first of the at least two user interaction elements is automatically adapted when the present control value for a second of the at least two user interaction elements is changed using the selector of the second of the at least two user interaction elements. A valid/executable range of control values for a particular user interaction element is determined based on (1) technical characteristics of the multichannel lighting unit such the number and spectral composition of the different channels of the multichannel lighting unit and maximum power consumption of the lighting unit, and (2) operational characteristics of the multichannel lighting unit such as selected control values for other user interaction elements. Control values may include luminous/radiant power or luminous/radiant flux, luminous/radiant intensity, illuminance/irradiance or any other value representing an amount of light/radiation. In general, the dynamic range of a user interaction elements will be a subrange of the static range of that user interaction elements. In embodiments, the dynamic range excludes the end points of the static range, more specifically a minimum control value of the dynamic range of a user interaction element is larger than a minimum control value of the static range for that user interaction element. In embodiment, the selector may overlay the dynamic scale, which itself overlays the static scale. Some or all of the static scale, dynamic scale and selector may be semitransparent such that they maintain visible to the user or operator of the user interface even when overlaid.", "relevant_specification_3": "When comparing figures 1A and 1B , the dynamic range 12 of user interaction element 10 associated with the blue color is adapted when selector 23 of user interaction element 20 associated with the green color is changed to a new control value. The selector 13 of user interaction element 10 may now fall outside the range of valid/executable control values for that user interaction element."}, {"key_feature": "controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element,characterized in thatthe dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, andthe dynamic scale is depicted as a subrange of the static scale and overlaying said static scale.", "relevant_specification_1": "Also described herein is a method of controlling a multichannel lighting unit wherein each channel of the multichannel lighting unit comprises at least one light source for emitting light having a channel-specific spectral composition, the spectral composition associated with one channel being different from the spectral composition associated with another channel, a light output from each channel being individually controllable, the method comprising: providing at least two user interaction elements, each user interaction element associated with a color producible with the multichannel lighting, wherein the color associated with at least one of the at least two user interaction elements does not have a directly corresponding channel in the multichannel lighting unit, each user interaction element comprising: a static scale representing a static range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the range of control values in the static scale is determined by a minimum and maximum light output, in the associated color, of the multichannel lighting unit, a dynamic scale representing a dynamic range of control values for controlling a light output of the multichannel lighting unit in respect of the color associated with the user interaction element, wherein the dynamic range of control values is a valid/executable range determined at least in part based on a present control value selected in respect of another user interaction element, and wherein the dynamic scale is depicted as a subrange of the static scale and overlaying said static scale, a selector adjustable by a user of the user interface for selecting a present control value for controlling the light output of the multichannel lighting unit in respect of the color associated with the user interaction element; selecting, using the selector of a first of the at least two user interaction elements, a present control value for controlling the light output of the one user interaction element; and adapting the dynamic scale of a second of the at least two user interaction elements, based on the present control value selected in respect of the first user interaction element; and controlling the light output of each channel of the multichannel lighting unit, based on a control value in respect of the color associated with the first user interaction element.", "relevant_specification_2": "The dynamic range of a first of the at least two user interaction elements is automatically adapted when the present control value for a second of the at least two user interaction elements is changed using the selector of the second of the at least two user interaction elements. A valid/executable range of control values for a particular user interaction element is determined based on (1) technical characteristics of the multichannel lighting unit such the number and spectral composition of the different channels of the multichannel lighting unit and maximum power consumption of the lighting unit, and (2) operational characteristics of the multichannel lighting unit such as selected control values for other user interaction elements. Control values may include luminous/radiant power or luminous/radiant flux, luminous/radiant intensity, illuminance/irradiance or any other value representing an amount of light/radiation. In general, the dynamic range of a user interaction elements will be a subrange of the static range of that user interaction elements. In embodiments, the dynamic range excludes the end points of the static range, more specifically a minimum control value of the dynamic range of a user interaction element is larger than a minimum control value of the static range for that user interaction element. In embodiment, the selector may overlay the dynamic scale, which itself overlays the static scale. Some or all of the static scale, dynamic scale and selector may be semitransparent such that they maintain visible to the user or operator of the user interface even when overlaid.", "relevant_specification_3": "The invention is based on the idea of providing a dynamic light output range taking into account the technicalities listed above, e.g. the interactions between colors and power limitations, and guide an operators, lighting designer or light protocol developer through the process in defining a valid specific mix of light colors with a specific light intensity executable with the (multichannel) lighting unit(s) of a lighting system. Such dynamic light ouput range provides a range of control values for light output of a particular color between a minimum exectuable light output and a maximum executable light output, i.a. based on the actual light output settings for other colors. The dynamic light output range may be presented in addition to an static light output range providing a range of control values for light ouptut of that same particular color between an absolute minimum and absolute maximum light output as determined by the specifications of the lighting unit or lighting system itself and not taking into account actual light output settings of other colors. Phrased differently, the static light output range may regarded as a 'design range', as it is determined by design of the lighting unit/lighting system, whereas the dynamic light output range may be regarded as an `operational range'. Per adjustment made in a light setting or light protocol, the dynamic ranges for all the light colors adjust themselves, where the static ranges are fixed and remain the same."}], "novelty_summary": "This invention provides a dynamic user interface for controlling a multichannel lighting unit where the user adjusts colors rather than the underlying hardware channels. Each color control features a static scale showing the absolute maximum range and an overlaid dynamic scale showing the currently valid and executable range. Crucially, the dynamic range for one color control automatically adjusts in real-time based on the selected settings of other colors, accounting for system limitations like power constraints and spectral overlap between different light sources."}