import json
import os
import sqlite3
import time
import uuid

import uvicorn
from dotenv import load_dotenv
from fastapi import HTTPException
from google.adk.agents import LlmAgent
from google.adk.cli.fast_api import get_fast_api_app
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.adk.tools import google_search
from google.genai import types

from database import store_infringement_output, store_novelty_output, store_reverse_engineering_output, \
    get_stored_outputs, store_claim_chart_output
from models import PatentRequest, InfringementRequest, ReverseEngineeringRequest, ClaimChartRequest, InfringementResponse, ReverseEngineeringResponse
from prompts import REVERSE_ENGINEERING_INFRINGEMENT_PROMPT, SYNTHESIS_PROMPT, INFRINGEMENT_AGENT_PROMPT, \
    CLAIM_CHART_GENERATOR_PROMPT
from utils.claim_chart_to_excel import create_excel_from_claim_charts
from utils.get_patent_full_details_tool import get_patent_data
from utils.litigation_competitor_search_tool import get_competitors
from utils.spec_support import get_spec_support
from utils.get_standard import get_standard_data

load_dotenv()
MODEL = os.getenv("MODEL_ID")

AGENT_DIR = os.path.dirname(os.path.abspath(__file__))
SESSION_SERVICE_URI = "sqlite:///./sessions.db"
ALLOWED_ORIGINS = ["*"]
SERVE_WEB_INTERFACE = False

app = get_fast_api_app(
    agents_dir=AGENT_DIR,
    session_service_uri=SESSION_SERVICE_URI,
    allow_origins=ALLOWED_ORIGINS,
    web=SERVE_WEB_INTERFACE,
)


@app.get("/health")
async def health_check():
    return {"status": "ok"}

@app.post("/novelty-agent/run")
async def run_novelty_agent(req: PatentRequest):
    patent_number = req.patent_number

    print(f"Starting analysis for patent: {patent_number}")

    print("Fetching patent data...")
    patent_data = get_patent_data(patent_number=patent_number)
    if not patent_data or "error" in patent_data:
        return {"error": f"FATAL: Could not fetch patent data for {patent_number}. Aborting."}

    assignees = patent_data.get("assignees", [])
    if not assignees:
        print(f"Warning: No assignees found for {patent_number}.")

    print(f"Fetching competitors for assignees: {assignees}...")
    competitor_data = get_competitors(patent_number, assignees)
    if isinstance(competitor_data, list):
        competitors = competitor_data


    print("Fetching specification support data...")
    spec_support_data = {}
    for i in range(3):  # Retry loop for reliability
        spec_support_data = await get_spec_support(patent_number=patent_number)
        if spec_support_data and spec_support_data.get("keyfeatures_list"):
            print("Successfully fetched spec support data.")
            break
        print(f"Warning: Spec support data was empty or invalid. Retrying... (Attempt {i + 1})")
        time.sleep(2)  # Wait before retrying
    else:
        print("FATAL: Could not retrieve valid spec support data after multiple attempts. Aborting.")
        return None

    print("Assembling data for final synthesis...")

    classification_codes = []
    classifications = patent_data.get("classifications", [])
    for classification in classifications:
        if "code" in classification:
            classification_codes.append(classification["code"])

    input_for_agent = {
        "patent_number": patent_number,
        "title": patent_data.get("title", ""),
        "abstract": patent_data.get("abstract"),
        "assignees": assignees,
        "inventors": patent_data.get("inventors", []),
        "application_date": patent_data.get("application_date"),
        "priority_date": patent_data.get("priority_date"),
        "grant_date": patent_data.get("grant_date"),
        "expiration_date": patent_data.get("expiration_date"),
        "patent_family": patent_data.get("patent_family", []),
        "classifications": classification_codes,
        "forward_citation_assignees": patent_data.get("forward_citation_assignees"),
        "competitors": competitors,
        "description": patent_data.get("description"),
        "keyconcepts": spec_support_data.get("keyconcepts"),
        "keyfeature_list": spec_support_data.get("keyfeatures_list"),
        "spec_support": spec_support_data.get("spec_support")
    }

    synthesis_agent = LlmAgent(
        name="patent_synthesis_agent",
        model=MODEL,
        instruction=SYNTHESIS_PROMPT.format(
            input_data_json=json.dumps(input_for_agent, indent=2)
        ),
        output_key="novelty_agent_output",
    )

    print("Invoking synthesis agent to generate the final JSON...")

    APP_NAME = "novelty_agent_app"
    USER_ID = "default_user"
    SESSION_ID = str(uuid.uuid4())

    session_service = InMemorySessionService()
    await session_service.create_session(app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID)

    runner = Runner(
        agent=synthesis_agent,
        app_name=APP_NAME,
        session_service=session_service
    )

    trigger_message = "Generate the novelty summary and final JSON output as instructed."
    content = types.Content(
        role="user",
        parts=[types.Part(text=trigger_message)]
    )

    # Run the agent and extract the response
    events = runner.run(
        user_id=USER_ID,
        session_id=SESSION_ID,
        new_message=content
    )

    final_json_output_str = None
    for event in events:
        if event.is_final_response():
            final_json_output_str = event.content.parts[0].text
            break

    if not final_json_output_str:
        raise ValueError("No final response received from the agent.")

    final_json_output_str = final_json_output_str.replace("```json", "").replace("```", "")
    if final_json_output_str.strip().startswith("{"):
        final_json_output_str = final_json_output_str.strip()
    print(final_json_output_str)
    final_data_obj = json.loads(final_json_output_str)

    store_novelty_output(patent_number, final_data_obj)
    print(f"Stored novelty output for patent {patent_number}")

    return final_data_obj

@app.post("/infringement-agent")
async def run_infringement_agent(req: InfringementRequest):
    patent_number = req.patent_number

    infringement_agent = LlmAgent(
        name="infringement_agent",
        model=MODEL,
        description=(
            "You are the Expert Patent Infringement Analyst, arguably the best in the world."
            "Your goal is to find products that may infringe on a given patent."
            "Use the tools available to you to find products that may infringe on a given patent and support litigation"
        ),
        instruction=INFRINGEMENT_AGENT_PROMPT.format(
            input_data_json=req.model_dump_json()
        ),
        output_key="infringement_agent_output",
        tools=[
            google_search,
        ],
    )

    print("Invoking infringement agent to generate the final JSON...")

    APP_NAME = "infringement_agent_app"
    USER_ID = "default_user"
    SESSION_ID = str(uuid.uuid4())

    session_service = InMemorySessionService()
    await session_service.create_session(app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID)

    runner = Runner(
        agent=infringement_agent,
        app_name=APP_NAME,
        session_service=session_service
    )

    trigger_message = "Extract infringing products."
    content = types.Content(
        role="user",
        parts=[types.Part(text=trigger_message)]
    )

    events = runner.run(
        user_id=USER_ID,
        session_id=SESSION_ID,
        new_message=content
    )

    final_json_output_str = None
    for event in events:
        if event.is_final_response():
            final_json_output_str = event.content.parts[0].text
            break

    if not final_json_output_str:
        raise ValueError("No final response received from the agent.")

    final_json_output_str = final_json_output_str.replace("```json", "").replace("```", "")
    if final_json_output_str.strip().startswith("{"):
        final_json_output_str = final_json_output_str.strip()
    print(final_json_output_str)

    final_data_obj = json.loads(final_json_output_str)

    # Validate the new structure
    if "products" not in final_data_obj or "spec_support" not in final_data_obj:
        raise ValueError("Invalid response structure: missing 'products' or 'spec_support' fields")

    store_infringement_output(patent_number, final_json_output_str)
    print(f"Stored infringement output for patent {patent_number}")
    return final_data_obj


@app.post("/reverse-engineering-search")
async def run_reverse_engineering_search(req: ReverseEngineeringRequest):

    patent_number = req.patent_number
    print("REQUEST:" , req.model_dump_json())
    reverse_engineering_agent = LlmAgent(
        name="reverse_engineering_agent",
        model=os.getenv("MODEL_ID"),
        instruction=REVERSE_ENGINEERING_INFRINGEMENT_PROMPT.format(
            input_data_json=req.model_dump_json()
        ),
        output_key="reverse_engineering_output",
    )

    print("Invoking reverse engineering agent to generate the final JSON...")

    APP_NAME = "reverse_engineering_app"
    USER_ID = "default_reverse_engineering_search_user"
    SESSION_ID = str(uuid.uuid4())

    session_service = InMemorySessionService()
    await session_service.create_session(app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID)

    runner = Runner(
        agent=reverse_engineering_agent,
        app_name=APP_NAME,
        session_service=session_service
    )

    trigger_message = "Extract more products applying reverse engineering techniques."
    content = types.Content(
        role="user",
        parts=[types.Part(text=trigger_message)]
    )

    events = runner.run(
        user_id=USER_ID,
        session_id=SESSION_ID,
        new_message=content
    )

    final_json_output_str = None
    for event in events:
        if event.is_final_response():
            final_json_output_str = event.content.parts[0].text
            break

    if not final_json_output_str:
        raise ValueError("No final response received from the agent.")

    final_json_output_str = final_json_output_str.replace("```json", "").replace("```", "")
    if final_json_output_str.strip().startswith("{"):
        final_json_output_str = final_json_output_str.strip()
    print(final_json_output_str)
    final_data_obj = json.loads(final_json_output_str)

    # Validate the new structure
    if "products" not in final_data_obj or "spec_support" not in final_data_obj:
        raise ValueError("Invalid response structure: missing 'products' or 'spec_support' fields")

    store_reverse_engineering_output(patent_number, final_data_obj)
    print(f"Stored reverse engineering output for patent {patent_number}")
    return final_data_obj

@app.post("/claim-chart-agent")
async def run_claim_chart_agent(req: ClaimChartRequest):
    patent_number = req.patent_number

    print("REQUEST:" , req.model_dump_json())
    standards_data = get_standard_data(patent_number)
    req_dict = req.model_dump()
    req_dict["standards_data"] = standards_data

    # Ensure we have the complete spec_support data for claim chart generation
    if not req_dict.get("spec_support"):
        raise ValueError("Missing spec_support data required for claim chart generation")

    input_data_json = json.dumps(req_dict, indent=2)

    claim_chart_agent = LlmAgent(
        name="claim_chart_agent",
        model=MODEL,
        instruction=CLAIM_CHART_GENERATOR_PROMPT.format(
            input_data_json=input_data_json
        )
    )

    print("Invoking claim chart agent to generate the final JSON...")

    APP_NAME = "claim_chart_agent_app"
    USER_ID = "default_user"
    SESSION_ID = str(uuid.uuid4())

    session_service = InMemorySessionService()
    await session_service.create_session(app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID)

    runner = Runner(
        agent=claim_chart_agent,
        app_name=APP_NAME,
        session_service=session_service
    )

    trigger_message = "Create claim charts"
    content = types.Content(
        role="user",
        parts=[types.Part(text=trigger_message)]
    )

    events = runner.run(
        user_id=USER_ID,
        session_id=SESSION_ID,
        new_message=content
    )

    final_json_output_str = None
    for event in events:
        if event.is_final_response():
            final_json_output_str = event.content.parts[0].text
            break

    if not final_json_output_str:
        raise ValueError("No final response received from the agent.")

    final_json_output_str = final_json_output_str.replace("```json", "").replace("```", "")
    if final_json_output_str.strip().startswith("{"):
        final_json_output_str = final_json_output_str.strip()
    print(final_json_output_str)
    final_data_obj = json.loads(final_json_output_str)

    store_claim_chart_output(patent_number, final_data_obj)
    print(f"Stored claim chart output for patent {patent_number}")

    create_excel_from_claim_charts(final_data_obj)
    print("Excel report created successfully")
    return final_data_obj

@app.get("/stored-outputs/{patent_number}")
async def get_patent_outputs(patent_number: str):
    """Retrieve all stored outputs for a given patent number"""
    try:
        stored_data = get_stored_outputs(patent_number)
        if stored_data:
            return stored_data
        else:
            raise HTTPException(status_code=404, detail=f"No stored outputs found for patent {patent_number}")
    except Exception as e:
        print(f"Error retrieving stored outputs for {patent_number}: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving data: {str(e)}")

if __name__ == "__main__":
    os.environ["GOOGLE_API_KEY"] = os.getenv("GOOGLE_API_KEY")
    uvicorn.run(app, host="0.0.0.0", port=int(os.environ.get("PORT", 8111)))