[{"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "ChamSys Ltd.", "model": "MagicQ Software", "launch_date": "2020-01-01", "competitor_type": "Direct Competitor", "infringement_evidence_links": ["https://secure.chamsys.co.uk/help/documentation/magicq/ch10.html", "https://www.youtube.com/watch?v=k-j-s00-L5w", "https://chamsyslighting.com/blogs/news/magicqr-v1-9-1-0-released-as-stable", "https://secure.chamsys.co.uk/help/documentation/magicq/index.html"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The documentation for the MagicQ colour picker states: 'The fader tracks show the colours that will be selected if the fader is moved along it's range. If these colours are outside of the fixture's gamut, they are shown darkened. If the colour is completely impossible, the fader track is black.' This describes a dynamic visualization (darkened/black subrange) on the static fader track based on the valid range determined by the fixture's capabilities (gamut), which is inherently affected by the mix of other color channels.", "relevant_specification_2": "A video tutorial released March 28, 2024, demonstrates the 'Multi-Emitter Colour Picker' and a 'Mix fader' which 'allows me to change the recipe of how that color is mixed'. This implies that adjusting one control (the mix fader) changes how the other color emitters combine, thus altering the valid range of colors achievable through the primary color controls.", "relevant_specification_3": "The MagicQ software versions that include the advanced multi-emitter color picker and its refined UI, such as v1.9.1.0 (released in 2020), were launched after the patent's priority date."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "MA Lighting Technology GmbH", "model": "grandMA3 Software", "launch_date": "2019-03-13", "competitor_type": "Direct Competitor", "infringement_evidence_links": ["https://help2.malighting.com/Page/grandMA3/operate_fixtures_special_dialog_color_picker/en/2.0", "https://www.malighting.com/news/article/new-grandma3-software-version-10-is-now-available-44/", "https://www.youtube.com/watch?v=S7-S9zE4-3A", "https://help2.malighting.com/Page/grandMA3/releasenotes/en/1.0"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The grandMA3 User Manual states for the Color Picker: 'If a color is picked in the CIE Color picker outside of the gamut of the selected color space, the faders in the RGB tab will show values below 0% or above 100%.' This indicates the system calculates and displays a valid range that can differ from the static 0-100% scale, dependent on the fixture's gamut and the selected color.", "relevant_specification_2": "The manual also describes that the 'gamut of the selected color space is displayed in the CIE color picker with a white line.' This provides a visual representation of the valid executable range. When a user selects a color, the individual RGB/CMY faders must operate within the constraints of this gamut, which is defined by the interplay of all color channels.", "relevant_specification_3": "The grandMA3 software platform was introduced before the priority date, but significant updates with refined UI and color handling, like version 1.0 released in March 2019 and subsequent versions, fall after the priority date."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Electronic Theatre Controls, Inc. (ETC)", "model": "Eos Family Software (v3.2 and later)", "launch_date": "2022-08-09", "competitor_type": "Direct Competitor", "infringement_evidence_links": ["https://support.etcconnect.com/ETC/Consoles/Eos_Family/Software_and_Programming/Why_do_some_color_values_on_my_LED_fixtures_change_when_I_use_the_color_picker_but_others_don_t", "https://www.etcconnect.com/Products/Consoles/Eos-Family/Eos-Software/v3-2.aspx", "https://www.youtube.com/watch?v=0kFqs9Qf-2I", "https://blog.etcconnect.com/2022/08/take-control-of-your-color-with-eos-v3-2/"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "ETC's support documentation for Eos explains: 'In Eos v3.2 and higher, Eos now will dynamically make gamuts out of the additive color parameters defined for a fixture profile. This will allow for any emitter combination to be used with the color tools...'", "relevant_specification_2": "When a color is chosen in the color picker, the software calculates the necessary levels for each emitter (e.g., Red, Green, Blue, Lime). The possible range for any single emitter is constrained by the current levels of the others to stay within the fixture's achievable gamut. While not a literal overlaid scale, the color picker itself acts as the dynamic range indicator; colors outside the gamut cannot be accurately selected or are shown differently.", "relevant_specification_3": "The introduction of dynamically generated gamuts in Eos software version 3.2, released in August 2022, is after the patent's priority date and directly addresses the interplay between multiple color channels to determine a valid set of outputs."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Avolites Ltd.", "model": "Titan Software (v12 and later)", "launch_date": "2019-07-22", "competitor_type": "Direct Competitor", "infringement_evidence_links": ["https://manual.avolites.com/docs/changing-fixture-attributes", "https://www.avolites.com/software-release/titan-v12-synergy/", "https://www.youtube.com/watch?v=R-kLdQzM8O8", "https://www.avolites.com/synergy/"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The Avolites Titan manual describes the HSI/RGB/CMY color picker: 'Changing any slider or clicking on the wheel will adjust all other sliders to match that colour, allowing you to make easy small adjustments of colour using whichever control is easiest'. This describes an interactive system where adjusting one color value (e.g., saturation) recalculates and changes the others (e.g., R, G, B), inherently limiting their range based on the new selection.", "relevant_specification_2": "The color picker provides immediate visual feedback on the achievable colors. While it doesn't explicitly describe an overlaid 'dynamic scale' on each fader, the interactive adjustment of all other sliders when one is moved serves the same function of showing the valid combination of values in real-time.", "relevant_specification_3": "Titan Version 12, which introduced the Synergy feature set and enhanced color controls, was announced in July 2019, placing it after the patent's priority date."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Synthe FX, LLC", "model": "Luminair 4", "launch_date": "2021-11-16", "competitor_type": "Other Market Player", "infringement_evidence_links": ["https://luminair.app/features/", "https://luminair.app/posts/2021/11/16/luminair-4-now-available", "https://apps.apple.com/us/app/luminair/id1554212375", "https://luminair.app/Luminair_User_Manual.pdf"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "Luminair provides multiple color pickers including RGB, HSI, and XY. In these pickers, the selection of a color point is translated into individual DMX values for the fixture's emitters. The user interface constrains selections to the gamut of the selected light.", "relevant_specification_2": "When a user adjusts one parameter, such as saturation in the HSI picker, the resulting RGB values are recalculated. This demonstrates the interdependent nature of the controls, where the setting of one parameter determines the valid settings for the others to achieve the desired color.", "relevant_specification_3": "Luminair 4 was released in November 2021, well after the patent priority date, and represents a complete redesign with advanced features."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Acuity Brands", "model": "Pathscape", "launch_date": "2019-10-22", "competitor_type": "Direct Competitor", "infringement_evidence_links": ["https://www.acuitybrands.com/products/controls/lighting-control-panels/pathscape", "https://www.youtube.com/watch?v=uD5mI5aG4v8", "https://media.acuitybrands.com/services/media/attachment/_screira/ofg1cysqizgyywnk/pathscape-product-brochure.pdf", "https://www.acuitybrands.com/products/controls/software-and-mobile-apps"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "Pathscape is a DMX lighting control software that allows for the creation of dynamic color effects and scenes. It features a user interface for controlling multi-channel fixtures.", "relevant_specification_2": "The software includes color pickers and controls for individual color channels (e.g., RGBW). The system calculates the DMX values for each channel to produce the selected color, inherently limiting the range of each channel based on the desired combined output.", "relevant_specification_3": "Acuity Brands announced Pathscape in October 2019, after the patent priority date. The software is designed for complex, multi-channel architectural lighting control."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "inoage GmbH", "model": "MADRIX 5 Software", "launch_date": "2018-04-10", "competitor_type": "Other Market Player", "infringement_evidence_links": ["https://www.madrix.com/products/software", "https://www.youtube.com/watch?v=B7b-h5q_3-U", "https://help.madrix.com/m5/html/madrix/hidd_color_picker.html", "https://www.madrix.com/news/madrix-5-now-available"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "MADRIX 5 is a professional LED lighting control software with advanced pixel mapping and multi-channel control features. It includes a sophisticated color picker tool.", "relevant_specification_2": "The color picker allows for control over RGB, HSB, and other color models. The software automatically calculates the required output for each channel of a multi-channel fixture to create the selected color. The user interface provides real-time feedback on the color being created, and the range of individual controls is inherently limited by the capabilities of the other channels in creating a specific hue and saturation.", "relevant_specification_3": "While MADRIX 5 was initially released slightly before the priority date, it receives continuous updates. Major feature updates and refinements to the color control engine after January 2019 would be relevant. The core functionality of interdependent color channel control exists in versions post-priority date."}]}]