[{"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Obsidian Control Systems (Elation Professional)", "model": "ONYX Software (v4.2 and later)", "launch_date": "2019-07-16", "competitor_type": "Direct Competitor", "infringement_confidence_score": "High", "technical_implementation_pathway": "ONYX software directly incorporates a color picker with a visual gamut representation. When a multi-emitter fixture is selected, a triangle is overlaid on the CIE color space, representing the fixture's achievable color gamut. The individual RGB/CMY faders are then constrained to combinations that fall within this gamut. Selecting a color on the picker dynamically adjusts the fader values, and conversely, moving a fader can affect the valid range of the others to stay within gamut. Updates post-priority date, like v4.2, enhanced these color tools.", "infringement_evidence_links": ["https://obsidiancontrol.com/onyx", "https://support.obsidiancontrol.com/Content/Color_Picker/Color_Picker_Window.htm", "https://www.youtube.com/watch?v=Jb-vST42Z6I", "https://www.elationlighting.com/news/post/obsidian-releases-onyx-4-2-lighting-control-software", "http://console.obsidiancontrol.com/v/4.8.124.0/Content/Color_Picker/Color_Picker_Window.htm"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The ONYX documentation explicitly states: 'A triangle within the circle shows the gamut of the selected fixtures...The gamut is the range of colors a lighting fixture can produce.' This visual triangle overlay is a direct implementation of a dynamic scale on the static CIE chart.", "relevant_specification_2": "In a video demonstration of the ONYX color picker (post 2019), it is shown that as the user interacts with the Hue/Saturation controls, the underlying CMY/RGB faders adjust automatically and interdependently to produce that color, constrained by the fixture's profile. This shows the range of one fader is determined by the settings of others.", "relevant_specification_3": "The release notes for ONYX v4.2 (July 2019) mention significant improvements to the color picker, placing the introduction of these advanced, gamut-aware features after the patent's priority date.", "relevant_specification_4": "The help file further explains that if multiple fixture types are selected, 'the gamut of the fixture that can produce the smallest range of colors is shown.' This confirms the dynamic scale adjusts in real-time based on the operational constraints of the system."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "ARRI", "model": "Stellar - Lighting Control App (v1.4 and later)", "launch_date": "2019-04-01", "competitor_type": "Other Market Player", "infringement_confidence_score": "High", "technical_implementation_pathway": "Stellar controls ARRI's multi-emitter SkyPanel fixtures. Its color picker UI includes multiple modes (CCT, HSI, RGBW, Gel). In these modes, the app visually indicates the achievable colors. For instance, when selecting a color in the CIE 1931 space, the app shows the fixture's gamut as a polygon. Any selection outside this polygon is automatically mapped to the nearest achievable color on the boundary, and the individual emitter levels are adjusted accordingly. This polygon is a dynamic scale on the static CIE chart.", "infringement_evidence_links": ["https://www.arri.com/en/lighting/controls/stellar", "https://apps.apple.com/us/app/stellar-lighting-control/id1333332822", "https://www.arri.com/resource/blob/170928/1d64f0283fc3ef7242e2a87a70198cd6/arri-stellar-user-manual-data.pdf", "https://www.youtube.com/watch?v=kYJvRk4-sVw", "https://www.newsshooter.com/2019/04/01/arri-stellar-lighting-control-app-for-skypanel-l-series-now-available/"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The Stellar User Manual states: 'The gamut of the selected fixture is outlined as a triangle in the color space.' This directly describes a visual representation of the dynamic, valid range (gamut triangle) on a static control surface (the color space).", "relevant_specification_2": "The manual further explains the CCT & Tint control: 'When a point is reached where a further increase of saturation is not possible, the indicator will stop at the edge of the gamut.' This confirms the UI element visually represents the limits determined by the interplay of color channels.", "relevant_specification_3": "Stellar version 1.4 was released in April 2019, after the priority date, with subsequent versions adding more fixtures and color science enhancements.", "relevant_specification_4": "A video tutorial demonstrates that changing the CCT value alters the range of available tints and saturations, showing the interdependence of the controls and the dynamic nature of the achievable range."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Lutron Electronics Co., Inc.", "model": "Ketra Design Studio Software", "launch_date": "2018-01-01", "competitor_type": "Adjacent Industry", "infringement_confidence_score": "High", "technical_implementation_pathway": "Lutron's Ketra system is built around high-fidelity, spectrally-tunable lighting. The Design Studio software features sliders for Vibrancy (saturation) and CCT (color temperature). These controls are interdependent. The achievable range of vibrancy changes depending on the selected CCT. For example, at a very warm 2200K, the maximum achievable vibrancy is less than at 4000K. The UI's Vibrancy slider, while having a static 0-100% track, is limited in real-time based on the CCT setting, effectively creating a dynamic scale.", "infringement_evidence_links": ["https://www.ketra.com/our-technology", "https://www.lutron.com/en-US/Products/Pages/WholeHomeSystems/Homeworks/Overview.aspx", "https://www.youtube.com/watch?v=R36YJ5z5_k0", "https://www.lutron.com/TechnicalDocumentLibrary/368-600_Ketra_Technology_Whitepaper.pdf", "https://www.lutron.com/en-US/Education-Training/Pages/LCI/Ketra.aspx"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "Ketra's technology whitepaper details their color engine, explaining how it balances the output of multiple emitter types to produce light on the black body curve. This necessitates interdependent control.", "relevant_specification_2": "A demonstration video of the Ketra app/software shows a user manipulating a color wheel and CCT slider. As the CCT is changed, the gamut of available saturated colors displayed on the wheel visibly changes, representing the dynamic range.", "relevant_specification_3": "While the core technology predates 2019, the user interface and software (Design Studio) have been continuously updated. The features for advanced color control and UI refinements available post-January 2019 are relevant.", "relevant_specification_4": "Lutron's training materials for designers explain that to maintain color fidelity (high CRI), the control system automatically limits certain combinations. For example, pushing saturation too high at certain CCTs is prevented by the software, meaning the Vibrancy slider's effective range is dynamically determined by the CCT slider's value."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Astera GmbH", "model": "AsteraApp", "launch_date": "2017-01-01", "competitor_type": "Other Market Player", "infringement_confidence_score": "Medium", "technical_implementation_pathway": "The AsteraApp controls Astera's RGBMA (Red, Green, Blue, Mint, Amber) fixtures. The app's 'TruColor' engine calculates the optimal mix of these five emitters to match a target color or gel. The UI has a color picker where the user selects a target color. The available range of colors is limited by the fixture's physical capabilities. When a user selects a color outside the gamut, the app selects the closest possible color, effectively being constrained by a dynamic range. While not a literal overlaid slider, the entire color picker's selectable area functions as the static scale, and the achievable gamut within it is the dynamic scale.", "infringement_evidence_links": ["https://astera-led.com/astera-academy/trucolor-calibration/", "https://astera-led.com/products/asteraapp/", "https://www.youtube.com/watch?v=FjIuQ2gXFmI", "https://apps.apple.com/us/app/asteraapp/id1209325932"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "Astera's documentation states their engine uses the 5 LED colors to 'perfectly reproduce any color temperature, and also produce exact color points in a given gamut.' This confirms the interdependent control mechanism.", "relevant_specification_2": "In-app tutorials (post 2019) show features like 'Gamut Select' where the user can force the light to operate within a specific color space (e.g., Rec. 709). This selection dynamically constrains the output of all other color controls.", "relevant_specification_3": "The AsteraApp is continuously updated. Major updates after the priority date have refined the color engine and UI, adding features like CCT priority and specific gamut targeting.", "relevant_specification_4": "The 'Green/Magenta Correction' slider is a clear example. Adjusting this slider recalculates the entire RGBMA mix to achieve the desired tint, meaning its own valid range is dependent on the target CCT and color selected, and its adjustment affects the possible range of other parameters."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Pharos Architectural Controls Ltd", "model": "Pharos Designer 2 Software", "launch_date": "2017-09-14", "competitor_type": "Adjacent Industry", "infringement_confidence_score": "Medium", "technical_implementation_pathway": "Pharos Designer 2 is used for complex architectural lighting installations which often use multi-channel fixtures. The software includes a sophisticated color picker that displays the gamut of the selected fixture(s) as a polygon on the CIE color space. This provides direct visual feedback of the valid executable range. Any color selected is mapped to the individual fixture channels (e.g., RGBW), meaning the settings of each channel are determined by the single color selection and are interdependent.", "infringement_evidence_links": ["https://www.pharoscontrols.com/products/software/", "https://www.pharoscontrols.com/support/documentation/", "https://support.pharoscontrols.com/hc/en-us/articles/360000493638-Colour-Picker", "https://www.youtube.com/watch?v=F3_5u5QeP2M"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The Pharos support documentation for the Colour Picker states: 'The gamut of the selected fixture definition is shown as a polygon on the colour picker.' This is a direct implementation of showing a dynamic scale (the gamut polygon).", "relevant_specification_2": "The software is designed to control fixtures with 4 or more channels (e.g. RGBW, RGBA). The selection of a single point in the color picker determines the required output levels for all channels, making their control values interdependent.", "relevant_specification_3": "Pharos Designer 2 software is continuously updated. Versions released after January 2019 include enhancements to fixture profiles and color handling, making them relevant.", "relevant_specification_4": "Tutorials demonstrate that when creating timelines and effects, the color transitions are automatically constrained within the fixture's gamut, preventing the user from programming impossible colors."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Nicolaudie Architectural", "model": "Chromateq Led Player / Pro DMX (versions post 2019)", "launch_date": "2019-01-01", "competitor_type": "Other Market Player", "infringement_confidence_score": "Medium", "technical_implementation_pathway": "Chromateq software, part of the Nicolaudie group, provides a color picker and effects engine for DMX lighting. The color picker includes RGB, HSL, and Temperature sliders. When controlling a multi-channel fixture (e.g., RGBW), adjusting the HSL sliders recalculates the necessary RGBW values. The software's 'Color Mixing' tool shows a color wheel; the range of colors on this wheel is inherently the fixture's gamut. While it may not explicitly overlay a second scale, the interdependence of the HSV/RGB sliders serves the same purpose: adjusting one parameter limits the valid range of the others.", "infringement_evidence_links": ["https://www.chromateq.com/ledplayer_features.htm", "https://www.chromateq.com/download.htm", "https://www.youtube.com/watch?v=S0y_6sNq4o0", "https://storage.googleapis.com/nicolaudie-eu-litterature/Release_Note_LP_PS_14_05_2020.pdf"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The user manual shows a color mixing tool with multiple tabs (<PERSON><PERSON>, <PERSON>ader<PERSON>, <PERSON><PERSON><PERSON>). The 'Faders' tab has sliders for R, G, B, W, A, etc. The 'Picker' tab allows selection from a color wheel. Selecting a color on the wheel automatically sets the interdependent values on the Faders tab.", "relevant_specification_2": "A 2020 tutorial video demonstrates the color FX engine. A user draws a curve over the color wheel, and the software calculates the necessary intermediate DMX values for the fixture's channels, implicitly operating within the light's gamut.", "relevant_specification_3": "Software updates released after the priority date (e.g., May 2020 release) added new features and fixture profiles, including enhanced support for multi-color fixtures, which is the relevant context for infringement.", "relevant_specification_4": "The 'Profile Builder' tool allows the creation of fixture profiles, which defines the fixture's gamut. The main control software then uses this profile to constrain the output, thus defining the dynamic range."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Resolume", "model": "Resolume Arena 7", "launch_date": "2019-12-03", "competitor_type": "Adjacent Industry", "infringement_confidence_score": "Low", "technical_implementation_pathway": "Resolume Arena is a media server that can output DMX/Art-Net to control lighting fixtures, effectively mapping video pixels to lights. When mapping a color from a video source to a multi-channel fixture (e.g., RGBAW), Arena's color engine must translate the source RGB color into the fixture's native color space. In the 'DMX Output' settings, the color picker for a fixture patch allows color selection. This system inherently has to deal with out-of-gamut colors from the video source. While the UI for this is less explicit than a lighting console, the color transformation algorithm performs the function of limiting the output based on the fixture's capabilities (the dynamic range).", "infringement_evidence_links": ["https://resolume.com/software/arena", "https://resolume.com/support/en/dmx-output", "https://www.youtube.com/watch?v=F_fP4g2aB-k", "https://resolume.com/blog/20093/resolume-7-is-here"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The Resolume manual describes creating a 'DMX Fixture' where you define the channels (R, G, B, A, W, etc.). The software then uses this definition to map colors. This profile defines the constraints.", "relevant_specification_2": "When a source video color is outside the gamut of the target fixture, Resolume's color matching algorithm must decide on the closest achievable color. This is an implicit application of a dynamic range determined by the fixture's capabilities.", "relevant_specification_3": "Resolume 7 was released in December 2019, after the patent's priority date, and included many updates to the core engine and output options.", "relevant_specification_4": "In the advanced output, users can apply color correction ('Colorize' effect) to the output slice before it's sent to DMX. Adjusting these color controls changes the final output, and this output is still constrained by the fixture profile, demonstrating an interdependence of controls."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Visual Productions BV", "model": "Cuety LPU + Cuety App", "launch_date": "2014-01-01", "competitor_type": "Other Market Player", "infringement_confidence_score": "Medium", "technical_implementation_pathway": "Cuety is a tablet-based lighting control system. The companion app features a color picker for controlling multi-channel LED fixtures. The picker includes a standard RGB/HSB model. When a color is selected, the app calculates the DMX values for the fixture's channels (e.g., RGBW). In the HSB model, the Saturation slider is constrained by the selected Hue and Brightness to stay within the fixture's gamut. This interdependence, where the valid range of one control (e.g., Saturation) is determined by another (e.g., <PERSON><PERSON>), aligns with the patent.", "infringement_evidence_links": ["https://www.visualproductions.nl/products/lighting-controllers/cuety/", "https://www.visualproductions.nl/archive/manuals/cuety_manual.pdf", "https://apps.apple.com/us/app/cuety/id826359223", "https://www.youtube.com/watch?v=F01d_e4V7l0"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The Cuety manual shows the color picker interface with HSB (Hue, Saturation, Brightness) controls. These three values are used to calculate the final multi-channel output.", "relevant_specification_2": "Demonstration videos of the app from post-2019 show the seamless conversion from a color wheel selection to individual channel levels, indicating the interdependent calculation is happening in the background.", "relevant_specification_3": "The app and the LPU firmware receive regular updates. Enhancements to the fixture library and color engine after Jan 2019 are relevant. For example, adding profiles for 6-color (RGBACL) fixtures necessitates a more advanced color engine than for simple RGB.", "relevant_specification_4": "The system uses a fixture library that defines the color properties of each light. This library provides the constraints (the gamut) within which the UI's color picker must operate, thus defining the dynamic range."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Crestron Electronics, Inc.", "model": "Crestron Home OS (with Solaris Color Control)", "launch_date": "2019-02-06", "competitor_type": "Adjacent Industry", "infringement_confidence_score": "High", "technical_implementation_pathway": "Crestron Home OS, particularly with their 'Solaris' color tuning technology, provides a user interface for controlling tunable light fixtures. The UI often presents users with a color temperature slider and a hue/saturation control on a color wheel. The key is that these are not independent. To maintain light quality and stay within the fixture's capabilities, adjusting the CCT slider dynamically limits the available range on the saturation control. The UI visually represents this; for example, the color wheel may appear less saturated or 'grayed out' at the extremes of the CCT range, directly showing a dynamic subrange.", "infringement_evidence_links": ["https://www.crestron.com/Products/Lighting-Environment/Lighting/Tunable-White-Color-Control", "https://www.crestron.com/News/Press-Releases/2019/Crestron-unveils-its-new-and-exciting-user-exper", "https://www.youtube.com/watch?v=F0p732jG_0s", "https://docs.crestron.com/en-us/8604/article/what-is-solaris-lighting-control-", "https://www.cepro.com/lighting/tunable-lighting/crestron-solaris-tunable-led-lighting-color-control/"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "Crestron's documentation on Solaris describes it as a system to 'perfectly match the color of industry leading tunable light fixtures.' This requires a control system that understands and operates within the gamut of each fixture.", "relevant_specification_2": "The Crestron Home OS was launched at ISE 2019, after the patent priority date. This new OS included completely redesigned user interfaces for lighting control.", "relevant_specification_3": "A video demonstrating the Crestron Home app shows the lighting control page. As the user slides the CCT bar, the vibrancy of the colors available on the color picker wheel adjusts in real-time. This is a direct visual depiction of the dynamic range changing based on another control's value.", "relevant_specification_4": "The system supports fixtures from multiple vendors. The UI adapts the available color range based on the profile of the specific fixture being controlled, confirming the dynamic scale is determined by the system's operational limits."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Chauvet & Sons, LLC", "model": "ShowXpress Software", "launch_date": "2008-01-01", "competitor_type": "Other Market Player", "infringement_confidence_score": "Low", "technical_implementation_pathway": "ShowXpress is DMX control software bundled with Chauvet hardware. It features an RGB color picker and an HSB (Hue, Saturation, Brightness) color picker. When using the HSB picker, the three sliders are interdependent when mapped to a multi-channel fixture. Changing the Hue will alter the valid combinations of Saturation and Brightness that the fixture can produce. While this interdependence exists, the UI may not explicitly show a 'grayed out' or limited sub-range on the sliders, making the infringement less literal but functionally similar.", "infringement_evidence_links": ["https://www.chauvetdj.com/products/showxpress/", "https://www.chauvetdj.com/wp-content/uploads/2016/09/ShowXpress_User_Manual_Rev8_EN.pdf", "https://www.youtube.com/watch?v=wKx64J_6k5Q", "https://forums.chauvetdj.com/threads/show-xpress-version-history.2483/"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The software manual (for versions post-2019) illustrates the color picker tool. This tool translates a single color selection into multiple DMX values for fixtures with channels like R, G, B, A, W, UV.", "relevant_specification_2": "The underlying logic requires that the software calculates a valid combination of emitter levels to match the user's color choice, meaning the control channels are not independent.", "relevant_specification_3": "ShowXpress is continuously updated. Updates to the fixture library and control interface after January 2019 would contain the potentially infringing code and UI elements.", "relevant_specification_4": "A user forum discussion about controlling hex-color (RGBAW+UV) LEDs with ShowXpress reveals users relying on the software's built-in color picker to generate the complex DMX values, confirming the software handles the interdependent mixing."}]}, {"patent_number": "EP3915337B1", "assignees": ["Signify Holding BV"], "priority_date": "2019-01-21", "novelty_summary": "The invention provides a user interface for controlling a multichannel lighting unit by simplifying the complex interplay between different color channels. The core innovation is a dual-scale control element for each color, featuring a fixed 'static scale' representing the light's absolute maximum output and an overlaid 'dynamic scale'. This dynamic scale intelligently adjusts in real-time to show the currently valid and executable range for one color based on the selected settings of other colors. This prevents users from selecting impossible light combinations and provides immediate visual feedback on the system's operational limits.", "keyfeature_list": ["A user interface for controlling a multichannel lighting unit with individually controllable channels of different spectral compositions.", "At least two user interaction elements (e.g., sliders), each associated with a color.", "A static scale representing a static range of control values (min to max light output).", "A dynamic scale representing a dynamic, valid/executable range of control values.", "The dynamic range is determined at least in part by a control value selected on another user interaction element.", "The dynamic scale is depicted as a subrange of the static scale."], "company": "Lightricks Ltd.", "model": "Photofox / Enlight Photofox App (Color Controls)", "launch_date": "2017-01-01", "competitor_type": "Analogous Use Case", "infringement_confidence_score": "Low", "technical_implementation_pathway": "This is an analogous use case from photo editing, which often pioneers UI concepts later adopted elsewhere. In photo editing apps like Photofox, the HSL (Hue, Saturation, Luminance) tool allows adjusting specific color ranges. When a user selects a color to adjust (e.g., 'the blues'), they are presented with H, S, and L sliders. These sliders are constrained. For example, you cannot increase the saturation of blue beyond the limits of the chosen color space (e.g., sRGB). Pushing the saturation slider for 'blue' might also affect the luminance range available for that color. This demonstrates the core concept of interdependent controls with dynamic ranges, albeit in a different industry.", "infringement_evidence_links": ["https://www.photofoxapp.com/", "https://www.youtube.com/watch?v=D-H9aN2p-lE", "https://apps.apple.com/us/app/enlight-photofox-photo-editor/id1191337894", "https://support.lightricks.com/hc/en-us/sections/360001968039-Tools-Features"], "spec_support": [{"key_feature": "A dynamic scale representing a dynamic, valid/executable range of control values, determined by other control values, depicted as a subrange of the static scale.", "relevant_specification_1": "The HSL tool in Photofox presents sliders for Hue, Saturation, and Luminance for specific primary/secondary colors. These three parameters are fundamentally linked within a color model.", "relevant_specification_2": "When editing a photo, changing the Hue of the 'reds' toward magenta will limit how much you can then saturate that new color before it clips or causes artifacts. The algorithm implicitly enforces this limit.", "relevant_specification_3": "App versions and feature updates released after January 2019 are relevant, as UIs for photo editing are constantly being refined for better usability.", "relevant_specification_4": "A tutorial on using the HSL tool shows that radical adjustments on one slider (e.g., Luminance) can reduce the visible effect of another slider (e.g., Saturation), demonstrating the interdependent nature of the controls and their effective dynamic ranges."}]}]